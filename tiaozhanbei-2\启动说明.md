# 南水北调水利问答助手 - 启动说明

## 🚀 一键启动方式

本项目提供了多种启动方式，选择适合您操作系统的方法：

### 方式一：Windows用户（推荐）
双击运行 `启动应用.bat` 文件

### 方式二：跨平台Python脚本
```bash
python start_app.py
```

### 方式三：Linux/Mac用户
```bash
./start_app.sh
```

### 方式四：传统Streamlit启动
```bash
streamlit run app.py
```

## 📋 启动前检查

启动脚本会自动检查以下项目：

### ✅ 必需项目
- [x] Python 3.8+ 版本
- [x] Streamlit 安装
- [x] 主要依赖包（torch, llama_index, chromadb等）
- [x] app.py 文件存在

### ⚠️ 可选项目
- [ ] 知识库文件（./storage/docstore.json）
- [ ] 向量数据库（./chroma_db/）

## 🔧 首次运行准备

### 1. 安装依赖
```bash
pip install streamlit torch llama_index chromadb pandas pypdf
```

### 2. 配置API密钥
编辑 `app.py` 文件，将第115行的API密钥替换为您的智谱AI密钥：
```python
api_key="your_zhipu_api_key_here",  # 替换为实际密钥
```

### 3. 构建知识库（可选）
如果需要使用本地知识库功能：
```bash
python build_knowledge_base.py
```

## 🌐 访问应用

启动成功后：
- 🖥️ 应用会自动在浏览器中打开
- 📱 默认地址：http://localhost:8501
- 🔄 如果8501端口被占用，会自动选择其他端口

## ❓ 常见问题

### Q: 启动失败怎么办？
A: 检查错误信息，通常是以下原因：
- Python版本过低（需要3.8+）
- 缺少依赖包
- API密钥未配置

### Q: 知识库相关错误？
A: 如果出现知识库加载错误：
1. 运行 `python build_knowledge_base.py` 构建知识库
2. 或者删除知识库检查，仅使用在线模式

### Q: 端口被占用？
A: 启动脚本会自动寻找可用端口（8501-8509）

### Q: 模型加载慢？
A: 首次运行需要下载和加载模型，请耐心等待

## 🛑 停止应用

- 在终端中按 `Ctrl+C`
- 或直接关闭终端窗口

## 📞 技术支持

如果遇到问题，请检查：
1. Python和依赖包版本
2. API密钥配置
3. 网络连接状态
4. 防火墙设置

---

**祝您使用愉快！** 🌊
