#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南水北调水利问答助手 - 一键启动脚本
作者: AI Assistant
创建时间: 2025-01-29
功能: 自动检查环境、依赖并启动Streamlit应用
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path
import threading
import signal


class AppLauncher:
    def __init__(self):
        self.app_file = "app.py"
        self.port = 8501
        self.host = "localhost"
        self.process = None
        
    def check_python_version(self):
        """检查Python版本"""
        print("🔍 检查Python版本...")
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ 错误: 需要Python 3.8或更高版本")
            print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
            return False
        print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
        return True
    
    def check_app_file(self):
        """检查应用文件是否存在"""
        print("🔍 检查应用文件...")
        if not Path(self.app_file).exists():
            print(f"❌ 错误: 找不到应用文件 {self.app_file}")
            print("   请确保在正确的目录下运行此脚本")
            return False
        print(f"✅ 应用文件检查通过: {self.app_file}")
        return True
    
    def check_streamlit(self):
        """检查Streamlit是否安装"""
        print("🔍 检查Streamlit安装...")
        try:
            import streamlit
            print(f"✅ Streamlit已安装: v{streamlit.__version__}")
            return True
        except ImportError:
            print("❌ 错误: Streamlit未安装")
            print("   请运行: pip install streamlit")
            return False
    
    def check_dependencies(self):
        """检查主要依赖是否安装"""
        print("🔍 检查主要依赖...")
        required_packages = [
            "torch",
            "llama_index", 
            "chromadb",
            "pandas",
            "pypdf"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                print(f"  ✅ {package}")
            except ImportError:
                print(f"  ❌ {package}")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\n❌ 缺少以下依赖包: {', '.join(missing_packages)}")
            print("   请运行: pip install " + " ".join(missing_packages))
            return False
        
        print("✅ 所有主要依赖检查通过")
        return True
    
    def check_knowledge_base(self):
        """检查知识库是否存在"""
        print("🔍 检查知识库...")
        required_dirs = ["./storage", "./chroma_db"]
        required_files = ["./storage/docstore.json"]
        
        missing_items = []
        
        for dir_path in required_dirs:
            if not Path(dir_path).exists():
                missing_items.append(f"目录: {dir_path}")
        
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_items.append(f"文件: {file_path}")
        
        if missing_items:
            print("⚠️  警告: 知识库未完全构建")
            for item in missing_items:
                print(f"   缺少: {item}")
            print("   建议运行: python build_knowledge_base.py")
            return False
        
        print("✅ 知识库检查通过")
        return True
    
    def find_available_port(self):
        """查找可用端口"""
        import socket
        for port in range(8501, 8510):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind((self.host, port))
                    self.port = port
                    return True
            except OSError:
                continue
        return False
    
    def start_streamlit(self):
        """启动Streamlit应用"""
        print(f"\n🚀 启动Streamlit应用...")
        print(f"   主机: {self.host}")
        print(f"   端口: {self.port}")
        print(f"   应用: {self.app_file}")
        
        # 构建启动命令
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            self.app_file,
            "--server.port", str(self.port),
            "--server.address", self.host,
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        try:
            # 启动进程
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            print("✅ Streamlit进程已启动")
            return True
            
        except Exception as e:
            print(f"❌ 启动失败: {str(e)}")
            return False
    
    def wait_for_server(self, timeout=30):
        """等待服务器启动"""
        print("⏳ 等待服务器启动...")
        import socket
        import time
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex((self.host, self.port))
                    if result == 0:
                        print("✅ 服务器已启动")
                        return True
            except:
                pass
            time.sleep(1)
            print(".", end="", flush=True)
        
        print("\n❌ 服务器启动超时")
        return False
    
    def open_browser(self):
        """打开浏览器"""
        url = f"http://{self.host}:{self.port}"
        print(f"🌐 正在打开浏览器: {url}")
        
        def open_url():
            time.sleep(2)  # 等待2秒确保服务器完全启动
            try:
                webbrowser.open(url)
                print("✅ 浏览器已打开")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {str(e)}")
                print(f"   请手动访问: {url}")
        
        # 在新线程中打开浏览器，避免阻塞
        threading.Thread(target=open_url, daemon=True).start()
    
    def setup_signal_handler(self):
        """设置信号处理器，优雅关闭"""
        def signal_handler(sig, frame):
            print("\n\n🛑 收到停止信号，正在关闭应用...")
            if self.process:
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                print("✅ 应用已关闭")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def run(self):
        """主运行函数"""
        print("=" * 60)
        print("🌊 南水北调水利问答助手 - 启动器")
        print("=" * 60)
        
        # 环境检查
        checks = [
            self.check_python_version,
            self.check_app_file,
            self.check_streamlit,
            self.check_dependencies,
        ]
        
        for check in checks:
            if not check():
                print("\n❌ 环境检查失败，请解决上述问题后重试")
                return False
        
        # 知识库检查（非强制）
        self.check_knowledge_base()
        
        # 查找可用端口
        if not self.find_available_port():
            print("❌ 无法找到可用端口")
            return False
        
        # 设置信号处理器
        self.setup_signal_handler()
        
        # 启动应用
        if not self.start_streamlit():
            return False
        
        # 等待服务器启动
        if not self.wait_for_server():
            if self.process:
                self.process.terminate()
            return False
        
        # 打开浏览器
        self.open_browser()
        
        # 显示运行信息
        print("\n" + "=" * 60)
        print("🎉 应用启动成功！")
        print(f"📱 访问地址: http://{self.host}:{self.port}")
        print("🔧 按 Ctrl+C 停止应用")
        print("=" * 60)
        
        # 等待进程结束
        try:
            self.process.wait()
        except KeyboardInterrupt:
            pass
        
        return True


def main():
    """主函数"""
    launcher = AppLauncher()
    success = launcher.run()
    
    if not success:
        print("\n❌ 启动失败")
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
