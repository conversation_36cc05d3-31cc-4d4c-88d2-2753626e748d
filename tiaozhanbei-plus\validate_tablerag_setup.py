# validate_tablerag_setup.py
"""
TableRAG集成验证脚本
验证TableRAG集成是否正确配置
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Tuple


class TableRAGValidator:
    """TableRAG集成验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
    
    def check(self, description: str, condition: bool, error_msg: str = "", warning_msg: str = ""):
        """执行检查"""
        self.total_checks += 1
        print(f"检查: {description}...", end=" ")
        
        if condition:
            print("✓ 通过")
            self.success_count += 1
            return True
        else:
            print("✗ 失败")
            if error_msg:
                self.errors.append(f"{description}: {error_msg}")
            if warning_msg:
                self.warnings.append(f"{description}: {warning_msg}")
            return False
    
    def check_file_exists(self, file_path: str, description: str = "") -> bool:
        """检查文件是否存在"""
        path = Path(file_path)
        desc = description or f"文件 {file_path}"
        return self.check(
            desc,
            path.exists(),
            f"文件不存在: {file_path}"
        )
    
    def check_directory_exists(self, dir_path: str, description: str = "") -> bool:
        """检查目录是否存在"""
        path = Path(dir_path)
        desc = description or f"目录 {dir_path}"
        return self.check(
            desc,
            path.exists() and path.is_dir(),
            f"目录不存在: {dir_path}"
        )
    
    def check_import(self, module_name: str, description: str = "") -> bool:
        """检查模块导入"""
        desc = description or f"导入 {module_name}"
        try:
            __import__(module_name)
            return self.check(desc, True)
        except ImportError as e:
            return self.check(desc, False, f"导入失败: {e}")
    
    def validate_python_environment(self) -> bool:
        """验证Python环境"""
        print("\n=== Python环境检查 ===")
        
        # 检查Python版本
        version = sys.version_info
        version_ok = version.major >= 3 and version.minor >= 8
        self.check(
            f"Python版本 (当前: {version.major}.{version.minor})",
            version_ok,
            "需要Python 3.8或更高版本"
        )
        
        # 检查核心依赖
        core_modules = [
            ("pandas", "pandas"),
            ("numpy", "numpy"),
            ("transformers", "transformers"),
            ("datasets", "datasets"),
            ("sentence_transformers", "sentence-transformers"),
            ("tqdm", "tqdm")
        ]
        
        for module, package in core_modules:
            self.check_import(module, f"核心依赖 {package}")
        
        return len(self.errors) == 0
    
    def validate_file_structure(self) -> bool:
        """验证文件结构"""
        print("\n=== 文件结构检查 ===")
        
        # 检查主要文件
        main_files = [
            ("app.py", "主应用文件"),
            ("tablerag_adapter.py", "TableRAG适配器"),
            ("tablerag_config.py", "TableRAG配置模块"),
            ("tablerag_retriever.py", "TableRAG检索器"),
            ("tablerag_vector_enhancer.py", "TableRAG向量增强器"),
            ("build_knowledge_base.py", "知识库构建脚本")
        ]
        
        for file_path, desc in main_files:
            self.check_file_exists(file_path, desc)
        
        # 检查TableRAG主目录
        self.check_directory_exists("tablerag-main", "TableRAG主目录")
        self.check_directory_exists("tablerag-main/src", "TableRAG源码目录")
        
        # 检查必要的子目录
        tablerag_dirs = [
            "tablerag-main/src/table_master",
            "tablerag-main/src/table_loader",
            "tablerag-main/src/llm",
            "tablerag-main/src/colbert_pipeline"
        ]
        
        for dir_path in tablerag_dirs:
            self.check_directory_exists(dir_path, f"TableRAG子目录 {dir_path}")
        
        # 检查数据目录
        data_dirs = [
            "data",
            "tablerag_cache",
            "logs"
        ]
        
        for dir_path in data_dirs:
            self.check_directory_exists(dir_path, f"数据目录 {dir_path}")
        
        return True
    
    def validate_tablerag_imports(self) -> bool:
        """验证TableRAG模块导入"""
        print("\n=== TableRAG模块导入检查 ===")
        
        try:
            # 测试主要模块导入
            from tablerag_adapter import create_tablerag_adapter
            self.check("TableRAG适配器导入", True)
            
            from tablerag_config import get_tablerag_config
            self.check("TableRAG配置导入", True)
            
            from tablerag_retriever import create_tablerag_query_processor
            self.check("TableRAG检索器导入", True)
            
            from tablerag_vector_enhancer import create_tablerag_vector_enhancer
            self.check("TableRAG向量增强器导入", True)
            
            # 测试配置创建
            config = get_tablerag_config("basic")
            self.check("TableRAG配置创建", config is not None)
            
            return True
            
        except Exception as e:
            self.check("TableRAG模块导入", False, str(e))
            return False
    
    def validate_app_integration(self) -> bool:
        """验证应用集成"""
        print("\n=== 应用集成检查 ===")
        
        try:
            # 检查app.py中的TableRAG配置
            app_file = Path("app.py")
            if app_file.exists():
                with open(app_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键配置
                checks = [
                    ("USE_TABLERAG", "TableRAG启用配置"),
                    ("TABLERAG_CONFIG_NAME", "TableRAG配置名称"),
                    ("EXCEL_PROCESSING_MODE", "Excel处理模式"),
                    ("load_excels_enhanced", "增强Excel加载函数"),
                    ("create_tablerag_fusion_retriever", "TableRAG融合检索器")
                ]
                
                for key, desc in checks:
                    self.check(desc, key in content, f"app.py中缺少 {key}")
            
            return True
            
        except Exception as e:
            self.check("应用集成检查", False, str(e))
            return False
    
    def validate_configuration(self) -> bool:
        """验证配置文件"""
        print("\n=== 配置文件检查 ===")
        
        # 检查配置文件
        config_files = [
            "tablerag_config.json",
            "config.json"  # 如果存在的话
        ]
        
        for config_file in config_files:
            if Path(config_file).exists():
                self.check_file_exists(config_file, f"配置文件 {config_file}")
                
                try:
                    import json
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    self.check(f"{config_file} 格式", isinstance(config_data, dict))
                except Exception as e:
                    self.check(f"{config_file} 格式", False, f"配置文件格式错误: {e}")
        
        return True
    
    def run_validation(self) -> bool:
        """运行完整验证"""
        print("TableRAG集成验证")
        print("=" * 50)
        
        # 执行各项检查
        self.validate_python_environment()
        self.validate_file_structure()
        self.validate_tablerag_imports()
        self.validate_app_integration()
        self.validate_configuration()
        
        # 输出结果
        print("\n" + "=" * 50)
        print("验证结果")
        print("=" * 50)
        print(f"总检查项: {self.total_checks}")
        print(f"通过: {self.success_count}")
        print(f"失败: {len(self.errors)}")
        print(f"警告: {len(self.warnings)}")
        
        if self.errors:
            print("\n错误:")
            for error in self.errors:
                print(f"  ✗ {error}")
        
        if self.warnings:
            print("\n警告:")
            for warning in self.warnings:
                print(f"  ⚠ {warning}")
        
        success_rate = (self.success_count / self.total_checks) * 100 if self.total_checks > 0 else 0
        print(f"\n成功率: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✓ TableRAG集成验证通过！")
            return True
        elif success_rate >= 70:
            print("⚠ TableRAG集成基本正常，但有一些问题需要解决")
            return False
        else:
            print("✗ TableRAG集成存在严重问题，需要修复")
            return False


def main():
    """主函数"""
    validator = TableRAGValidator()
    success = validator.run_validation()
    
    if not success:
        print("\n建议:")
        print("1. 运行 python install_tablerag.py 重新安装")
        print("2. 检查依赖是否正确安装")
        print("3. 确保tablerag-main目录完整")
        print("4. 检查配置文件是否正确")
        sys.exit(1)
    else:
        print("\n可以开始使用TableRAG功能了！")


if __name__ == "__main__":
    main()
