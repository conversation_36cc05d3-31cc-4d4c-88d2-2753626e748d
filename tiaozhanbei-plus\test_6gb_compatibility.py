# test_6gb_compatibility.py
"""
6GB显存兼容性测试脚本
测试TableRAG在6GB显存环境下的运行情况
"""

import torch
import psutil
import gc
from memory_optimizer import MemoryOptimizer, check_system_compatibility, apply_memory_optimizations


def test_memory_requirements():
    """测试内存需求"""
    print("=== 6GB显存兼容性测试 ===")
    
    # 检查系统兼容性
    if not check_system_compatibility():
        print("❌ 系统不满足最低要求")
        return False
    
    # 应用内存优化
    apply_memory_optimizations()
    
    # 创建内存优化器
    optimizer = MemoryOptimizer(max_gpu_memory_gb=5.5)  # 为6GB显存预留0.5GB
    
    # 显示当前内存状态
    info = optimizer.get_memory_info()
    print(f"\n当前内存状态:")
    print(f"- CPU内存: {info['cpu_memory']['percent']:.1f}% 使用中")
    print(f"- 可用CPU内存: {info['cpu_memory']['available']:.1f}GB")
    
    if torch.cuda.is_available() and "gpu_memory" in info:
        print(f"- GPU显存: {info['gpu_memory']['total']:.1f}GB 总计")
        print(f"- GPU已用: {info['gpu_memory']['allocated']:.2f}GB")
        print(f"- GPU可用: {info['gpu_memory']['free']:.2f}GB")
        
        if info['gpu_memory']['total'] < 6.5:
            print("✓ 检测到6GB级别显存，将使用优化配置")
        else:
            print("ℹ️ 显存充足，可以使用标准配置")
    else:
        print("- GPU: 未检测到或不可用，将使用CPU模式")
    
    return True


def test_tablerag_low_memory():
    """测试TableRAG低内存模式"""
    print("\n=== TableRAG低内存模式测试 ===")
    
    try:
        from tablerag_config import get_tablerag_config
        from tablerag_adapter import create_tablerag_adapter
        
        # 使用低内存配置
        config = get_tablerag_config("low_memory")
        print(f"✓ 低内存配置加载成功")
        print(f"  - 表格大小限制: {config.max_table_size}")
        print(f"  - 内容长度限制: {config.max_content_length}")
        print(f"  - 并行处理: {config.parallel_processing}")
        
        # 创建适配器
        adapter = create_tablerag_adapter(config.to_dict())
        print("✓ TableRAG适配器创建成功（低内存模式）")
        
        return True
        
    except Exception as e:
        print(f"✗ TableRAG低内存模式测试失败: {e}")
        return False


def test_excel_processing_memory():
    """测试Excel处理的内存使用"""
    print("\n=== Excel处理内存测试 ===")
    
    try:
        import pandas as pd
        import tempfile
        from pathlib import Path
        
        # 创建测试数据（小规模）
        data = {
            '项目': ['项目A', '项目B', '项目C'] * 10,  # 30行数据
            '状态': ['进行中', '已完成', '计划中'] * 10,
            '预算': [100, 200, 300] * 10
        }
        
        optimizer = MemoryOptimizer()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test_small.xlsx"
            df = pd.DataFrame(data)
            df.to_excel(test_file, index=False)
            
            # 监控内存使用情况下处理Excel
            def process_excel():
                from app import _load_excels_simple
                return _load_excels_simple(temp_dir)
            
            # 使用内存监控处理
            result = optimizer.monitor_memory_during_processing(process_excel)
            
            print(f"✓ Excel处理完成，生成 {len(result)} 条数据")
            
            # 检查内存使用是否在安全范围内
            if optimizer.check_memory_usage():
                print("✓ 内存使用在安全范围内")
                return True
            else:
                print("⚠️ 内存使用较高，但处理完成")
                return True
                
    except Exception as e:
        print(f"✗ Excel处理内存测试失败: {e}")
        return False


def test_model_loading():
    """测试模型加载"""
    print("\n=== 模型加载测试 ===")
    
    try:
        optimizer = MemoryOptimizer()
        
        def load_embedding_model():
            from llama_index.embeddings.huggingface import HuggingFaceEmbedding
            # 使用较小的模型进行测试
            model_path = "sentence-transformers/all-MiniLM-L6-v2"
            embed_model = HuggingFaceEmbedding(model_name=model_path)
            return embed_model
        
        # 监控模型加载的内存使用
        embed_model = optimizer.monitor_memory_during_processing(load_embedding_model)
        print("✓ 嵌入模型加载成功")
        
        # 清理模型
        del embed_model
        optimizer.cleanup_memory()
        print("✓ 模型清理完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型加载测试失败: {e}")
        return False


def provide_optimization_recommendations():
    """提供优化建议"""
    print("\n=== 6GB显存优化建议 ===")
    
    info = MemoryOptimizer().get_memory_info()
    
    recommendations = []
    
    # CPU内存建议
    if info['cpu_memory']['total'] < 16:
        recommendations.append("💡 建议升级到16GB或更多CPU内存以获得更好性能")
    
    # GPU显存建议
    if torch.cuda.is_available() and "gpu_memory" in info:
        gpu_total = info['gpu_memory']['total']
        if gpu_total <= 6:
            recommendations.extend([
                "🔧 使用 EXCEL_PROCESSING_MODE = 'simple' 减少显存占用",
                "🔧 使用 TABLERAG_CONFIG_NAME = 'low_memory' 配置",
                "🔧 设置较小的 CHUNK_SIZE (256) 和 SIMILARITY_TOP_K (3)",
                "🔧 考虑关闭重排序功能以节省显存",
                "🔧 使用批处理大小为1，避免并行处理"
            ])
    
    # 通用建议
    recommendations.extend([
        "📝 定期清理缓存目录",
        "📝 关闭不必要的后台程序",
        "📝 使用SSD存储以提高I/O性能",
        "📝 考虑使用量化模型减少内存占用"
    ])
    
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")


def main():
    """主测试函数"""
    print("TableRAG 6GB显存兼容性测试")
    print("=" * 50)
    
    tests = [
        ("内存需求检查", test_memory_requirements),
        ("TableRAG低内存模式", test_tablerag_low_memory),
        ("Excel处理内存", test_excel_processing_memory),
        ("模型加载", test_model_loading)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    # 输出结果
    print("\n" + "=" * 50)
    print("6GB显存兼容性测试结果")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed / total) * 100:.1f}%")
    
    # 提供优化建议
    provide_optimization_recommendations()
    
    if passed >= total * 0.75:
        print(f"\n✅ 系统基本兼容6GB显存环境")
        print("建议使用以下配置:")
        print("- TABLERAG_CONFIG_NAME = 'low_memory'")
        print("- EXCEL_PROCESSING_MODE = 'simple'")
        print("- 启用内存监控和定期清理")
        return True
    else:
        print(f"\n⚠️ 系统可能在6GB显存环境下遇到问题")
        print("建议:")
        print("- 考虑升级硬件")
        print("- 使用CPU模式")
        print("- 减少并发处理")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        import sys
        sys.exit(1)
