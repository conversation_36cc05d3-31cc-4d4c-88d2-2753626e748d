# build_kb_optimized.py
"""
6GB显存优化的知识库构建脚本
分批处理PDF，避免显存溢出
"""

import os
import shutil
import time
from pathlib import Path
import chromadb
from llama_index.core import VectorStoreIndex, StorageContext, Settings
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.vector_stores.chroma import ChromaVectorStore

# 导入应用模块和内存优化器
from app import (
    Config,
    load_excels_enhanced,
    create_nodes_from_excel,
    create_nodes_from_pdf,
    init_node_parser,
    init_models
)
from memory_optimizer import MemoryOptimizer, memory_safe_wrapper


class OptimizedKnowledgeBaseBuilder:
    """优化的知识库构建器"""
    
    def __init__(self):
        self.memory_optimizer = MemoryOptimizer(max_gpu_memory_gb=5.5)
        self.batch_size = 2  # 每批处理2个PDF文件
        self.max_pdf_pages = 20  # 限制单个PDF最大页数
        
    def check_pdf_size(self, pdf_path: Path) -> bool:
        """检查PDF文件大小是否适合处理"""
        try:
            # 简单的文件大小检查（MB）
            size_mb = pdf_path.stat().st_size / (1024 * 1024)
            if size_mb > 50:  # 超过50MB的PDF跳过
                print(f"⚠️ 跳过大文件: {pdf_path.name} ({size_mb:.1f}MB)")
                return False
            return True
        except Exception as e:
            print(f"⚠️ 检查文件大小失败: {pdf_path.name}, {e}")
            return False
    
    @memory_safe_wrapper
    def process_pdf_batch(self, pdf_files: list, batch_num: int) -> list:
        """分批处理PDF文件"""
        print(f"\n处理PDF批次 {batch_num}: {len(pdf_files)} 个文件")
        
        # 显示当前批次的文件
        for pdf_file in pdf_files:
            print(f"  - {pdf_file.name}")
        
        try:
            from app import load_pdfs_from_files
            
            # 处理这批PDF文件
            pdf_data = []
            for pdf_file in pdf_files:
                if self.check_pdf_size(pdf_file):
                    try:
                        # 单独处理每个PDF
                        single_pdf_data = load_pdfs_from_files([str(pdf_file)])
                        pdf_data.extend(single_pdf_data)
                        print(f"✓ 处理完成: {pdf_file.name}")
                        
                        # 检查内存使用
                        if not self.memory_optimizer.check_memory_usage():
                            print("⚠️ 内存使用过高，执行清理...")
                            self.memory_optimizer.cleanup_memory()
                            time.sleep(2)  # 等待清理完成
                            
                    except Exception as e:
                        print(f"✗ 处理失败: {pdf_file.name}, {e}")
                        continue
            
            print(f"✓ 批次 {batch_num} 处理完成，生成 {len(pdf_data)} 条数据")
            return pdf_data
            
        except Exception as e:
            print(f"✗ 批次 {batch_num} 处理失败: {e}")
            return []
    
    def build_knowledge_base(self):
        """构建优化的知识库"""
        print("=== 6GB显存优化知识库构建 ===")
        
        try:
            # 1. 初始化模型
            print("\n步骤 1/7: 初始化模型...")
            embed_model, llm, rerank_model = init_models()
            Settings.embed_model = embed_model
            Settings.llm = llm
            print("✓ 模型初始化完成")
            
            # 2. 初始化节点解析器
            print("\n步骤 2/7: 初始化节点解析器...")
            node_parser = init_node_parser()
            print("✓ 节点解析器初始化完成")
            
            # 3. 清理旧的知识库
            print("\n步骤 3/7: 清理旧的知识库...")
            db_dir = Path(Config.VECTOR_DB_DIR)
            persist_dir = Path(Config.PERSIST_DIR)
            
            if db_dir.exists():
                shutil.rmtree(db_dir)
                print("✓ 清理向量数据库目录")
            
            if persist_dir.exists():
                shutil.rmtree(persist_dir)
                print("✓ 清理持久化目录")
            
            # 4. 检查文件
            print(f"\n步骤 4/7: 检查 {Config.DATA_DIR} 中的文件...")
            data_dir = Path(Config.DATA_DIR)
            pdf_files = list(data_dir.rglob("*.pdf"))
            excel_files = list(data_dir.rglob("*.xlsx"))
            
            print(f"发现 {len(pdf_files)} 个PDF文件, {len(excel_files)} 个Excel文件")
            
            # 5. 处理Excel文件
            print(f"\n步骤 5/7: 处理Excel文件...")
            excel_data = []
            excel_nodes = []
            
            if excel_files:
                excel_data = load_excels_enhanced(Config.DATA_DIR)
                excel_nodes = create_nodes_from_excel(excel_data, node_parser)
                print(f"✓ Excel处理完成: {len(excel_data)} 条数据, {len(excel_nodes)} 个节点")
            else:
                print("ℹ️ 未发现Excel文件")
            
            # 6. 分批处理PDF文件
            print(f"\n步骤 6/7: 分批处理PDF文件...")
            all_pdf_data = []
            
            if pdf_files:
                # 按文件大小排序，先处理小文件
                pdf_files.sort(key=lambda x: x.stat().st_size)
                
                # 分批处理
                for i in range(0, len(pdf_files), self.batch_size):
                    batch = pdf_files[i:i + self.batch_size]
                    batch_num = i // self.batch_size + 1
                    
                    # 处理当前批次
                    batch_data = self.process_pdf_batch(batch, batch_num)
                    all_pdf_data.extend(batch_data)
                    
                    # 批次间休息，让GPU降温
                    if i + self.batch_size < len(pdf_files):
                        print("⏳ 批次间休息5秒...")
                        time.sleep(5)
                
                # 创建PDF节点
                pdf_nodes = create_nodes_from_pdf(all_pdf_data, node_parser)
                print(f"✓ PDF处理完成: {len(all_pdf_data)} 条数据, {len(pdf_nodes)} 个节点")
            else:
                pdf_nodes = []
                print("ℹ️ 未发现PDF文件")
            
            # 7. 构建向量索引
            print(f"\n步骤 7/7: 构建向量索引...")
            all_nodes = excel_nodes + pdf_nodes
            
            if not all_nodes:
                print("❌ 没有可用的节点数据")
                return False
            
            print(f"总节点数: {len(all_nodes)}")
            
            # 创建向量数据库
            db_dir.mkdir(parents=True, exist_ok=True)
            db = chromadb.PersistentClient(path=str(db_dir))
            chroma_collection = db.get_or_create_collection(Config.COLLECTION_NAME)
            vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
            
            # 创建存储上下文
            storage_context = StorageContext.from_defaults(vector_store=vector_store)
            
            # 分批构建索引以节省内存
            print("正在分批构建向量索引...")
            index_batch_size = 50  # 每批50个节点
            
            for i in range(0, len(all_nodes), index_batch_size):
                batch_nodes = all_nodes[i:i + index_batch_size]
                batch_num = i // index_batch_size + 1
                total_batches = (len(all_nodes) + index_batch_size - 1) // index_batch_size
                
                print(f"构建索引批次 {batch_num}/{total_batches}: {len(batch_nodes)} 个节点")
                
                if i == 0:
                    # 第一批创建索引
                    index = VectorStoreIndex(
                        nodes=batch_nodes,
                        storage_context=storage_context,
                        show_progress=True
                    )
                else:
                    # 后续批次添加到现有索引
                    index.insert_nodes(batch_nodes)
                
                # 检查内存使用
                if not self.memory_optimizer.check_memory_usage():
                    print("🧹 执行内存清理...")
                    self.memory_optimizer.cleanup_memory()
                    time.sleep(2)
            
            # 持久化存储
            persist_dir.mkdir(parents=True, exist_ok=True)
            index.storage_context.persist(persist_dir=str(persist_dir))
            
            print("✓ 向量索引构建完成")
            print(f"✓ 数据已持久化到 {persist_dir}")
            
            # 显示统计信息
            print("\n=== 知识库统计 ===")
            print(f"PDF文件数: {len(pdf_files)}")
            print(f"Excel文件数: {len(excel_files)}")
            print(f"PDF数据条数: {len(all_pdf_data)}")
            print(f"Excel数据条数: {len(excel_data)}")
            print(f"总节点数: {len(all_nodes)}")
            
            # 显示内存使用情况
            final_memory = self.memory_optimizer.get_memory_info()
            print(f"\n最终内存使用:")
            print(f"CPU: {final_memory['cpu_memory']['percent']:.1f}%")
            if 'gpu_memory' in final_memory:
                print(f"GPU: {final_memory['gpu_memory']['allocated']:.2f}GB")
            
            print("\n🎉 优化知识库构建成功！")
            return True
            
        except Exception as e:
            print(f"❌ 知识库构建失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("6GB显存优化知识库构建工具")
    print("=" * 50)
    
    # 检查数据目录
    data_dir = Path(Config.DATA_DIR)
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 应用内存优化
    from memory_optimizer import apply_memory_optimizations
    apply_memory_optimizations()
    
    # 创建构建器并运行
    builder = OptimizedKnowledgeBaseBuilder()
    success = builder.build_knowledge_base()
    
    if success:
        print("\n🚀 知识库构建完成！现在可以启动应用:")
        print("streamlit run app.py")
    else:
        print("\n❌ 知识库构建失败")
        print("建议:")
        print("1. 尝试运行 python build_excel_only_kb.py 仅构建Excel知识库")
        print("2. 减少PDF文件数量")
        print("3. 检查显存使用情况")


if __name__ == "__main__":
    main()
