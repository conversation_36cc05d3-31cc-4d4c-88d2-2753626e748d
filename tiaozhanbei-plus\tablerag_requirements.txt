# TableRAG集成所需的核心依赖
# 这些是运行TableRAG功能的最小依赖集

# 核心数据处理
pandas>=1.5.0
numpy>=1.24.0
openpyxl>=3.1.0

# 机器学习和NLP
transformers>=4.36.2
datasets>=2.11.0
sentence-transformers>=2.2.0
torch>=2.0.0

# 向量存储和检索
faiss-cpu>=1.7.2  # 如果有GPU，可以改为 faiss-gpu
tqdm>=4.65.0

# 文本处理
beautifulsoup4>=4.12.0
lxml>=4.9.0

# API调用（如果使用OpenAI）
openai>=1.14.0

# 可选依赖（根据需要安装）
# colbert-ai>=0.2.19  # 如果需要ColBERT功能
# ragatouille>=0.0.7  # 如果需要RAGatouille
# spacy>=3.6.0        # 如果需要spaCy功能
# nltk>=3.8.0         # 如果需要NLTK功能

# 注意：
# 1. 如果你已经安装了这些包的其他版本，可能会有冲突
# 2. 建议在虚拟环境中安装
# 3. 如果遇到版本冲突，可以尝试降低版本要求
