# quick_test_fix.py
"""
快速测试修复后的Pandas查询功能
"""

import pandas as pd
from pathlib import Path
from app import Config, load_excel_dataframes


def test_pandas_output_processor():
    """测试输出处理器"""
    print("=== 测试输出处理器 ===")
    
    # 模拟有问题的输出（包含markdown标记）
    problematic_output = """
根据您的查询，您需要获取十二里河渡槽进口节制闸在2020-01-01 12:00:00至2020-01-02 12:00:00期间的调度数据。

```python
# 首先检查时间列的数据类型和格式
print(df['时间'].dtype)

# 将时间列转换为datetime类型
df['时间'] = pd.to_datetime(df['时间'], format='%Y-%m-%d %H')

# 设置查询时间范围
start_time = '2020-01-01 12:00:00'
end_time = '2020-01-02 12:00:00'

# 筛选指定时间范围内的数据
report_df = df[(df['时间'] >= start_time) & (df['时间'] <= end_time)]

# 显示结果
if not report_df.empty:
    print(f"找到{len(report_df)}条记录:")
    print(report_df)
else:
    print("在指定时间范围内未找到记录。")
```

如果数据中没有包含2020-01-01的记录，我会提供数据的时间范围供参考。
"""
    
    try:
        # 导入输出处理器
        from app import custom_output_processor

        # 测试处理器
        cleaned_code = custom_output_processor(problematic_output, "测试查询")
        
        print("原始输出（前200字符）:")
        print(problematic_output[:200] + "...")
        
        print("\n处理后的代码:")
        print(cleaned_code)
        
        print("\n✓ 输出处理器测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 输出处理器测试失败: {e}")
        return False


def test_simple_pandas_queries():
    """测试简单的Pandas查询"""
    print("\n=== 测试简单Pandas查询 ===")
    
    try:
        # 加载Excel数据
        dataframes = load_excel_dataframes(Config.DATA_DIR)
        
        if not dataframes:
            print("❌ 未加载到DataFrame")
            return False
        
        print(f"✓ 加载了 {len(dataframes)} 个DataFrame")
        
        # 对每个DataFrame进行基础测试
        for name, df in dataframes.items():
            print(f"\n📊 测试DataFrame: {name}")
            
            # 测试基础查询
            test_queries = [
                "df.shape",
                "df.columns.tolist()",
                "df.head(3)",
                "df.dtypes",
                "len(df)"
            ]
            
            for query in test_queries:
                try:
                    result = eval(query)
                    print(f"✓ {query}: 成功")
                    if query == "df.shape":
                        print(f"   结果: {result}")
                    elif query == "len(df)":
                        print(f"   结果: {result} 行")
                except Exception as e:
                    print(f"✗ {query}: {e}")
            
            # 检查时间列
            time_columns = []
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['时间', 'time', '日期', 'date']):
                    time_columns.append(col)
            
            if time_columns:
                print(f"   时间列: {time_columns}")
                for col in time_columns[:1]:
                    try:
                        print(f"   {col} 数据类型: {df[col].dtype}")
                        print(f"   {col} 示例值: {df[col].dropna().head(2).tolist()}")
                        if df[col].dtype == 'object':
                            print(f"   建议: 需要转换为datetime类型")
                    except Exception as e:
                        print(f"   {col} 分析失败: {e}")
            
            break  # 只测试第一个DataFrame
        
        return True
        
    except Exception as e:
        print(f"❌ 简单查询测试失败: {e}")
        return False


def generate_safe_queries():
    """生成安全的查询建议"""
    print("\n=== 安全查询建议 ===")
    
    try:
        dataframes = load_excel_dataframes(Config.DATA_DIR)
        
        if not dataframes:
            print("❌ 无法生成建议")
            return
        
        for name, df in dataframes.items():
            print(f"\n📊 对于表格 '{name}':")
            print(f"   数据形状: {df.shape}")
            
            # 生成基于实际数据的查询建议
            safe_queries = [
                f"这个表格有 {len(df)} 行数据",
                f"表格有 {len(df.columns)} 列",
                "显示前5行数据",
                "显示数据的基本统计信息",
                "显示所有列名"
            ]
            
            # 基于列名的具体建议
            columns = df.columns.tolist()
            for col in columns[:3]:
                if df[col].dtype in ['int64', 'float64']:
                    safe_queries.append(f"'{col}' 字段的统计信息")
                elif any(keyword in col.lower() for keyword in ['时间', 'time', '日期']):
                    safe_queries.append(f"数据的时间范围")
                else:
                    safe_queries.append(f"'{col}' 字段的唯一值数量")
            
            print("   推荐查询:")
            for i, query in enumerate(safe_queries, 1):
                print(f"     {i}. {query}")
            
            break  # 只处理第一个表格
            
    except Exception as e:
        print(f"❌ 生成建议失败: {e}")


def main():
    """主函数"""
    print("Pandas查询修复验证")
    print("=" * 50)
    
    # 测试输出处理器
    processor_success = test_pandas_output_processor()
    
    # 测试简单查询
    query_success = test_simple_pandas_queries()
    
    # 生成安全查询建议
    generate_safe_queries()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"输出处理器: {'✓ 成功' if processor_success else '✗ 失败'}")
    print(f"简单查询: {'✓ 成功' if query_success else '✗ 失败'}")
    
    if processor_success and query_success:
        print("\n🎉 修复验证成功！")
        print("\n💡 现在可以尝试这些查询:")
        print("1. '这个表格有多少行数据？'")
        print("2. '显示前5行数据'")
        print("3. '表格的列名是什么？'")
        print("4. '数据的时间范围是什么？'")
        print("\n🚀 启动应用: streamlit run app.py")
    else:
        print("\n⚠️ 还有问题需要解决")
        print("建议检查Excel文件格式和数据结构")


if __name__ == "__main__":
    main()
