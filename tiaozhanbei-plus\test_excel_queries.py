# test_excel_queries.py
"""
测试Excel查询功能
验证修复后的查询效果
"""

from app import load_knowledge_base, Config
import pandas as pd
from pathlib import Path


def test_basic_queries():
    """测试基本查询功能"""
    print("=== 测试基本查询功能 ===")
    
    try:
        # 加载知识库
        print("加载知识库...")
        index = load_knowledge_base()
        
        if index is None:
            print("❌ 知识库加载失败")
            return False
        
        print("✓ 知识库加载成功")
        
        # 创建查询引擎
        query_engine = index.as_query_engine(similarity_top_k=3)
        
        # 测试查询列表
        test_queries = [
            # 基础数据探索查询
            "这个表格有多少行数据？",
            "表格的列名是什么？",
            "显示前5行数据",
            "数据的基本统计信息是什么？",
            
            # 数据内容查询
            "表格中包含什么类型的数据？",
            "数据的时间范围是什么？",
            "有哪些数值字段？",
            
            # 原始问题（可能失败的）
            "十二里河渡槽进口节制闸的调度报告",
            "2023年的数据情况",
        ]
        
        print(f"\n开始测试 {len(test_queries)} 个查询...")
        
        success_count = 0
        for i, query in enumerate(test_queries, 1):
            print(f"\n{'='*50}")
            print(f"测试 {i}/{len(test_queries)}: {query}")
            print("="*50)
            
            try:
                response = query_engine.query(query)
                print(f"✓ 查询成功")
                print(f"回答: {response}")
                
                # 检查回答质量
                response_str = str(response)
                if "找不到符合条件的结果" in response_str:
                    print("⚠️ 返回了'找不到结果'的消息")
                elif "建议尝试" in response_str or "df.head()" in response_str:
                    print("✓ 提供了有用的建议")
                    success_count += 1
                elif len(response_str) > 20:  # 有实质性内容
                    print("✓ 返回了有用的信息")
                    success_count += 1
                else:
                    print("⚠️ 回答内容较少")
                
            except Exception as e:
                print(f"✗ 查询失败: {e}")
        
        print(f"\n测试结果: {success_count}/{len(test_queries)} 个查询成功")
        return success_count > len(test_queries) * 0.5
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_direct_pandas():
    """直接测试Pandas DataFrame"""
    print("\n=== 直接测试Pandas DataFrame ===")
    
    try:
        from app import load_excel_dataframes
        
        # 加载Excel数据框
        dataframes = load_excel_dataframes(Config.DATA_DIR)
        
        if not dataframes:
            print("❌ 未加载到任何DataFrame")
            return False
        
        print(f"✓ 加载了 {len(dataframes)} 个DataFrame")
        
        for name, df in dataframes.items():
            print(f"\n📊 DataFrame: {name}")
            print(f"   形状: {df.shape}")
            print(f"   列名: {df.columns.tolist()}")
            
            # 显示前几行
            print("   前3行数据:")
            print(df.head(3))
            
            # 显示数据类型
            print("   数据类型:")
            for col, dtype in df.dtypes.items():
                print(f"     {col}: {dtype}")
            
            # 检查时间列
            time_columns = []
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['时间', 'time', '日期', 'date', '年', '月', '日']):
                    time_columns.append(col)
            
            if time_columns:
                print(f"   时间相关列: {time_columns}")
                for col in time_columns[:2]:  # 只显示前2个
                    try:
                        unique_count = df[col].nunique()
                        sample_values = df[col].dropna().head(3).tolist()
                        print(f"     {col}: {unique_count} 个唯一值, 示例: {sample_values}")
                    except Exception as e:
                        print(f"     {col}: 分析失败 - {e}")
            
            print("-" * 40)
        
        return True
        
    except Exception as e:
        print(f"❌ 直接Pandas测试失败: {e}")
        return False


def suggest_better_queries():
    """根据数据结构建议更好的查询"""
    print("\n=== 查询建议 ===")
    
    try:
        from app import load_excel_dataframes
        
        dataframes = load_excel_dataframes(Config.DATA_DIR)
        
        if not dataframes:
            print("❌ 无法加载数据，无法提供建议")
            return
        
        for name, df in dataframes.items():
            print(f"\n📊 对于表格 '{name}'，建议的查询:")
            
            # 基于实际列名的查询建议
            columns = df.columns.tolist()
            
            suggestions = [
                f"这个表格有 {len(df)} 行数据",
                f"表格包含以下列: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}",
                "显示数据的基本统计信息",
                "显示前10行数据"
            ]
            
            # 基于列名的具体建议
            for col in columns[:3]:  # 只处理前3列
                if df[col].dtype in ['int64', 'float64']:
                    suggestions.append(f"'{col}' 字段的最大值和最小值是多少？")
                elif 'time' in col.lower() or '时间' in col or '日期' in col:
                    suggestions.append(f"数据的时间范围是什么？")
                else:
                    suggestions.append(f"'{col}' 字段有哪些不同的值？")
            
            for i, suggestion in enumerate(suggestions, 1):
                print(f"   {i}. {suggestion}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ 生成建议失败: {e}")


def main():
    """主函数"""
    print("Excel查询功能测试")
    print("=" * 50)
    
    # 检查数据文件
    data_dir = Path(Config.DATA_DIR)
    excel_files = list(data_dir.rglob("*.xlsx"))
    
    if not excel_files:
        print("❌ 未找到Excel文件")
        return
    
    print(f"✓ 发现 {len(excel_files)} 个Excel文件")
    for file in excel_files:
        print(f"  - {file.name}")
    
    # 直接测试Pandas
    pandas_success = test_direct_pandas()
    
    if pandas_success:
        # 测试查询引擎
        query_success = test_basic_queries()
        
        # 提供查询建议
        suggest_better_queries()
        
        if query_success:
            print("\n🎉 测试成功！查询功能正常工作")
            print("\n💡 使用建议:")
            print("1. 使用简单、直接的查询语言")
            print("2. 先了解数据结构，再进行具体查询")
            print("3. 避免过于复杂的时间范围查询")
        else:
            print("\n⚠️ 查询功能需要进一步优化")
    else:
        print("\n❌ 基础数据加载有问题")
    
    print(f"\n🚀 现在可以启动应用测试: streamlit run app.py")


if __name__ == "__main__":
    main()
