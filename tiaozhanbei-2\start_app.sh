#!/bin/bash
# -*- coding: utf-8 -*-
# 南水北调水利问答助手 - Linux/Mac启动脚本

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}🌊 南水北调水利问答助手 - 启动器${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 错误: 未找到Python${NC}"
        echo -e "   请先安装Python 3.8或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 显示Python版本
echo -e "${GREEN}🔍 检查Python版本...${NC}"
$PYTHON_CMD --version

# 检查是否在正确目录
if [ ! -f "app.py" ]; then
    echo
    echo -e "${RED}❌ 错误: 未找到app.py文件${NC}"
    echo -e "   请确保在正确的项目目录下运行此脚本"
    exit 1
fi

echo
echo -e "${GREEN}🚀 正在启动应用...${NC}"
echo -e "${YELLOW}   如果是首次运行，可能需要较长时间加载模型${NC}"
echo

# 运行启动脚本
$PYTHON_CMD start_app.py

# 检查退出状态
if [ $? -ne 0 ]; then
    echo
    echo -e "${RED}❌ 启动过程中出现错误${NC}"
    read -p "按回车键退出..."
fi
