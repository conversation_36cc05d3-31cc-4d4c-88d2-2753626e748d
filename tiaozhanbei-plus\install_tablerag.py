# install_tablerag.py
"""
TableRAG集成安装脚本
自动安装TableRAG相关依赖并进行配置
"""

import os
import sys
import subprocess
import json
from pathlib import Path


def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n{'='*50}")
    print(f"执行: {description or command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print(f"警告: {result.stderr}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        print(f"输出: {e.stdout}")
        print(f"错误输出: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"错误: 需要Python 3.8或更高版本，当前版本: {version.major}.{version.minor}")
        return False
    print(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """安装依赖"""
    print("\n开始安装TableRAG依赖...")
    
    # 检查是否存在tablerag_requirements.txt
    req_file = Path("tablerag_requirements.txt")
    if not req_file.exists():
        print("错误: 找不到tablerag_requirements.txt文件")
        return False
    
    # 安装依赖
    success = run_command(
        f"pip install -r {req_file}",
        "安装TableRAG依赖包"
    )
    
    if not success:
        print("依赖安装失败，尝试逐个安装核心依赖...")
        core_deps = [
            "datasets>=2.14.0",
            "transformers>=4.36.2",
            "pandas>=1.5.0",
            "openpyxl>=3.1.0",
            "sentence-transformers>=2.2.0",
            "numpy>=1.24.0",
            "tqdm>=4.65.0"
        ]
        
        for dep in core_deps:
            run_command(f"pip install {dep}", f"安装 {dep}")
    
    return True


def setup_tablerag_directory():
    """设置TableRAG目录结构"""
    print("\n设置TableRAG目录结构...")
    
    # 创建必要的目录
    directories = [
        "tablerag_cache",
        "logs",
        "data/processed/table_outputs",
        "data/processed/retrieval_results",
        "data/processed/prediction"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {directory}")
    
    return True


def create_default_config():
    """创建默认配置文件"""
    print("\n创建默认配置文件...")
    
    # 检查是否已存在配置文件
    config_file = Path("tablerag_config.json")
    if config_file.exists():
        print("配置文件已存在，跳过创建")
        return True
    
    # 创建默认配置
    from tablerag_config import get_tablerag_config, save_tablerag_config
    
    try:
        config = get_tablerag_config("enhanced")
        success = save_tablerag_config(config, "tablerag_config.json")
        
        if success:
            print("默认配置文件创建成功: tablerag_config.json")
        else:
            print("配置文件创建失败")
            return False
            
    except Exception as e:
        print(f"创建配置文件时出错: {e}")
        return False
    
    return True


def test_tablerag_import():
    """测试TableRAG模块导入"""
    print("\n测试TableRAG模块导入...")
    
    try:
        from tablerag_adapter import create_tablerag_adapter
        from tablerag_config import get_tablerag_config
        from tablerag_retriever import create_tablerag_query_processor
        
        print("✓ TableRAG适配器导入成功")
        print("✓ TableRAG配置模块导入成功")
        print("✓ TableRAG检索器导入成功")
        
        # 测试配置创建
        config = get_tablerag_config("basic")
        print("✓ TableRAG配置创建成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ TableRAG模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ TableRAG测试失败: {e}")
        return False


def update_app_config():
    """更新应用配置"""
    print("\n更新应用配置...")
    
    try:
        # 检查app.py中的配置
        app_file = Path("app.py")
        if not app_file.exists():
            print("警告: 找不到app.py文件")
            return True
        
        # 读取app.py内容
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含TableRAG配置
        if "USE_TABLERAG" in content:
            print("✓ app.py已包含TableRAG配置")
        else:
            print("警告: app.py可能需要手动更新以包含TableRAG配置")
        
        return True
        
    except Exception as e:
        print(f"更新应用配置时出错: {e}")
        return False


def main():
    """主安装流程"""
    print("TableRAG集成安装程序")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，但可以继续进行其他设置")
    
    # 设置目录结构
    if not setup_tablerag_directory():
        print("目录设置失败")
        sys.exit(1)
    
    # 创建默认配置
    if not create_default_config():
        print("配置文件创建失败")
        sys.exit(1)
    
    # 测试导入
    if not test_tablerag_import():
        print("模块测试失败，请检查依赖安装")
        sys.exit(1)
    
    # 更新应用配置
    update_app_config()
    
    print("\n" + "="*50)
    print("TableRAG集成安装完成！")
    print("="*50)
    print("\n下一步:")
    print("1. 检查tablerag_config.json配置文件")
    print("2. 运行 python build_knowledge_base.py 重建知识库")
    print("3. 启动应用: streamlit run app.py")
    print("\n如果遇到问题，请检查:")
    print("- 所有依赖是否正确安装")
    print("- tablerag-main目录是否存在")
    print("- 配置文件是否正确")


if __name__ == "__main__":
    main()
