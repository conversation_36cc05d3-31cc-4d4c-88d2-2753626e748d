# check_excel_data.py
"""
检查Excel数据结构和内容
帮助诊断查询问题
"""

import pandas as pd
from pathlib import Path
from app import Config


def analyze_excel_data():
    """分析Excel数据结构"""
    print("=== Excel数据结构分析 ===")
    
    data_dir = Path(Config.DATA_DIR)
    excel_files = list(data_dir.rglob("*.xlsx"))
    
    if not excel_files:
        print("❌ 未找到Excel文件")
        return
    
    for excel_file in excel_files:
        print(f"\n📁 文件: {excel_file.name}")
        
        try:
            # 读取所有工作表
            sheets = pd.read_excel(excel_file, sheet_name=None, engine='openpyxl')
            
            for sheet_name, df in sheets.items():
                print(f"\n📊 工作表: {sheet_name}")
                print(f"   行数: {len(df)}")
                print(f"   列数: {len(df.columns)}")
                
                # 显示列名
                print(f"   列名: {list(df.columns)}")
                
                # 显示数据类型
                print(f"   数据类型:")
                for col, dtype in df.dtypes.items():
                    print(f"     {col}: {dtype}")
                
                # 显示前几行数据
                print(f"   前3行数据:")
                print(df.head(3).to_string())
                
                # 检查时间相关列
                time_columns = []
                for col in df.columns:
                    if any(keyword in col.lower() for keyword in ['时间', 'time', '日期', 'date', '年', '月', '日']):
                        time_columns.append(col)
                
                if time_columns:
                    print(f"   时间相关列: {time_columns}")
                    for col in time_columns:
                        print(f"     {col} 示例值: {df[col].dropna().head(3).tolist()}")
                
                # 检查数值列
                numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
                if numeric_columns:
                    print(f"   数值列: {numeric_columns}")
                    for col in numeric_columns[:3]:  # 只显示前3个数值列
                        print(f"     {col} 范围: {df[col].min()} ~ {df[col].max()}")
                
                print("-" * 50)
                
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")


def test_pandas_queries():
    """测试Pandas查询"""
    print("\n=== 测试Pandas查询 ===")
    
    try:
        from app import load_excel_dataframes
        
        # 加载Excel数据框
        dataframes = load_excel_dataframes(Config.DATA_DIR)
        
        if not dataframes:
            print("❌ 未加载到任何DataFrame")
            return
        
        print(f"✓ 加载了 {len(dataframes)} 个DataFrame")
        
        for name, df in dataframes.items():
            print(f"\n📊 测试DataFrame: {name}")
            print(f"   形状: {df.shape}")
            
            # 测试简单查询
            test_queries = [
                "df.head()",
                "df.info()",
                "df.describe()",
                "len(df)",
                "df.columns.tolist()"
            ]
            
            for query in test_queries:
                try:
                    result = eval(query)
                    print(f"   ✓ {query}: 成功")
                    if query == "df.columns.tolist()":
                        print(f"     结果: {result}")
                except Exception as e:
                    print(f"   ✗ {query}: {e}")
            
            # 测试时间范围查询（如果有时间列）
            time_columns = []
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['时间', 'time', '日期', 'date']):
                    time_columns.append(col)
            
            if time_columns:
                print(f"   时间列: {time_columns}")
                for col in time_columns[:1]:  # 只测试第一个时间列
                    try:
                        unique_values = df[col].dropna().unique()[:5]
                        print(f"     {col} 示例值: {unique_values}")
                    except Exception as e:
                        print(f"     {col} 分析失败: {e}")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def suggest_query_improvements():
    """建议查询改进方案"""
    print("\n=== 查询改进建议 ===")
    
    suggestions = [
        "1. 🔍 使用更通用的查询语言",
        "   - 避免具体的时间范围",
        "   - 使用表格中实际存在的列名",
        "   - 先询问数据概况，再深入细节",
        "",
        "2. 📊 推荐的查询示例:",
        "   - '这个表格有多少行数据？'",
        "   - '表格的列名是什么？'",
        "   - '显示前几行数据'",
        "   - '数据的时间范围是什么？'",
        "",
        "3. 🛠️ 系统优化建议:",
        "   - 启用向量知识库以支持语义查询",
        "   - 优化Pandas查询引擎的错误处理",
        "   - 添加数据预览功能"
    ]
    
    for suggestion in suggestions:
        print(suggestion)


def check_system_configuration():
    """检查系统配置"""
    print("\n=== 系统配置检查 ===")
    
    try:
        from app import Config
        
        print(f"数据目录: {Config.DATA_DIR}")
        print(f"向量数据库目录: {Config.VECTOR_DB_DIR}")
        print(f"持久化目录: {Config.PERSIST_DIR}")
        print(f"使用TableRAG: {Config.USE_TABLERAG}")
        print(f"TableRAG配置: {Config.TABLERAG_CONFIG_NAME}")
        print(f"Excel处理模式: {Config.EXCEL_PROCESSING_MODE}")
        
        # 检查目录是否存在
        dirs_to_check = [
            Config.DATA_DIR,
            Config.VECTOR_DB_DIR,
            Config.PERSIST_DIR
        ]
        
        for dir_path in dirs_to_check:
            path = Path(dir_path)
            if path.exists():
                print(f"✓ {dir_path} 存在")
            else:
                print(f"✗ {dir_path} 不存在")
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")


def main():
    """主函数"""
    print("Excel数据诊断工具")
    print("=" * 50)
    
    # 分析Excel数据
    analyze_excel_data()
    
    # 测试Pandas查询
    test_pandas_queries()
    
    # 检查系统配置
    check_system_configuration()
    
    # 提供改进建议
    suggest_query_improvements()
    
    print("\n" + "=" * 50)
    print("诊断完成！")
    print("\n💡 如果问题仍然存在，请尝试:")
    print("1. 使用更简单的查询语言")
    print("2. 重新构建知识库: python build_excel_only_kb.py")
    print("3. 检查Excel文件的数据格式")


if __name__ == "__main__":
    main()
