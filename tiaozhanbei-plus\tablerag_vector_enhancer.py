# tablerag_vector_enhancer.py
"""
TableRAG向量存储增强器
优化表格数据的向量化存储和检索
"""

from typing import List, Dict, Any, Optional
from llama_index.core.schema import TextNode, NodeWithScore
from llama_index.core.embeddings import BaseEmbedding
import numpy as np

# 尝试导入VectorStore，如果失败则使用Any类型
try:
    from llama_index.core.vector_stores.types import VectorStore
except ImportError:
    try:
        from llama_index.core.vector_stores import VectorStore
    except ImportError:
        print("警告: 无法导入VectorStore，使用Any类型替代")
        VectorStore = Any


class TableRAGVectorEnhancer:
    """TableRAG向量存储增强器"""
    
    def __init__(self, embedding_model: BaseEmbedding):
        self.embedding_model = embedding_model
        self.table_node_cache = {}  # 缓存表格节点的增强信息
    
    def enhance_table_nodes_for_indexing(self, nodes: List[TextNode]) -> List[TextNode]:
        """
        为索引优化表格节点
        
        Args:
            nodes: 原始节点列表
            
        Returns:
            增强后的节点列表
        """
        enhanced_nodes = []
        
        for node in nodes:
            if self._is_table_node(node):
                enhanced_node = self._enhance_table_node(node)
                enhanced_nodes.append(enhanced_node)
            else:
                enhanced_nodes.append(node)
        
        return enhanced_nodes
    
    def _is_table_node(self, node: TextNode) -> bool:
        """判断是否是表格节点"""
        metadata = node.metadata
        return (
            metadata.get("content_type") in ["excel_row", "excel_table"] or
            metadata.get("node_type") == "tablerag_enhanced" or
            metadata.get("processing_method") == "tablerag"
        )
    
    def _enhance_table_node(self, node: TextNode) -> TextNode:
        """增强单个表格节点"""
        try:
            # 创建增强的文本内容用于向量化
            enhanced_text = self._create_enhanced_text_for_embedding(node)
            
            # 创建新的节点
            enhanced_node = TextNode(
                text=enhanced_text,
                id_=node.id_,
                metadata={
                    **node.metadata,
                    "original_text": node.text,  # 保存原始文本
                    "enhanced_for_embedding": True,
                    "enhancement_timestamp": self._get_timestamp()
                }
            )
            
            # 缓存增强信息
            self.table_node_cache[node.id_] = {
                "original_text": node.text,
                "enhanced_text": enhanced_text,
                "metadata": node.metadata
            }
            
            return enhanced_node
            
        except Exception as e:
            print(f"增强表格节点 {node.id_} 失败: {e}")
            return node
    
    def _create_enhanced_text_for_embedding(self, node: TextNode) -> str:
        """为嵌入创建增强文本"""
        metadata = node.metadata
        text_parts = []
        
        # 添加表格类型信息
        if metadata.get("content_type"):
            text_parts.append(f"数据类型: {metadata['content_type']}")
        
        # 添加来源信息
        if metadata.get("source_file"):
            text_parts.append(f"来源文件: {metadata['source_file']}")
        
        if metadata.get("sheet_name"):
            text_parts.append(f"工作表: {metadata['sheet_name']}")
        
        # 添加处理方法信息
        if metadata.get("processing_method"):
            text_parts.append(f"处理方法: {metadata['processing_method']}")
        
        # 添加表格格式信息
        if metadata.get("table_format"):
            text_parts.append(f"表格格式: {metadata['table_format']}")
        
        # 添加原始内容
        text_parts.append("内容:")
        text_parts.append(node.text)
        
        # 如果有原始表格预览，添加关键词
        if metadata.get("raw_table_preview"):
            text_parts.append("表格结构关键词:")
            keywords = self._extract_table_keywords(metadata["raw_table_preview"])
            text_parts.append(" ".join(keywords))
        
        return "\n".join(text_parts)
    
    def _extract_table_keywords(self, table_preview: str) -> List[str]:
        """从表格预览中提取关键词"""
        keywords = []
        
        try:
            lines = table_preview.split('\n')
            if lines:
                # 提取表头作为关键词
                header_line = lines[0]
                if '|' in header_line:
                    headers = [h.strip() for h in header_line.split('|')]
                    keywords.extend(headers)
                
                # 提取一些数据值作为关键词
                for line in lines[2:5]:  # 取前几行数据
                    if '|' in line:
                        values = [v.strip() for v in line.split('|')]
                        # 只添加非空且不是纯数字的值
                        for value in values:
                            if value and not value.isdigit() and len(value) > 1:
                                keywords.append(value)
        
        except Exception as e:
            print(f"提取表格关键词失败: {e}")
        
        return keywords[:10]  # 限制关键词数量
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        import datetime
        return datetime.datetime.now().isoformat()
    
    def enhance_retrieval_results(self, results: List[NodeWithScore], query: str) -> List[NodeWithScore]:
        """
        增强检索结果
        
        Args:
            results: 原始检索结果
            query: 查询字符串
            
        Returns:
            增强后的检索结果
        """
        enhanced_results = []
        
        for node_with_score in results:
            node = node_with_score.node
            
            if self._is_table_node(node) and node.id_ in self.table_node_cache:
                # 恢复原始文本用于显示
                cached_info = self.table_node_cache[node.id_]
                
                # 创建用于显示的节点
                display_node = TextNode(
                    text=cached_info["original_text"],
                    id_=node.id_,
                    metadata={
                        **node.metadata,
                        "enhanced_for_display": True,
                        "query_context": query
                    }
                )
                
                enhanced_node_with_score = NodeWithScore(
                    node=display_node,
                    score=node_with_score.score
                )
                enhanced_results.append(enhanced_node_with_score)
            else:
                enhanced_results.append(node_with_score)
        
        return enhanced_results
    
    def get_table_statistics(self) -> Dict[str, Any]:
        """获取表格处理统计信息"""
        return {
            "cached_table_nodes": len(self.table_node_cache),
            "enhancement_methods": list(set(
                info["metadata"].get("processing_method", "unknown")
                for info in self.table_node_cache.values()
            )),
            "table_formats": list(set(
                info["metadata"].get("table_format", "unknown")
                for info in self.table_node_cache.values()
            ))
        }


class TableRAGAwareVectorStore:
    """TableRAG感知的向量存储包装器"""
    
    def __init__(self, base_vector_store: VectorStore, enhancer: TableRAGVectorEnhancer):
        self.base_vector_store = base_vector_store
        self.enhancer = enhancer
    
    def add_nodes(self, nodes: List[TextNode], **kwargs) -> List[str]:
        """添加节点到向量存储"""
        # 增强表格节点
        enhanced_nodes = self.enhancer.enhance_table_nodes_for_indexing(nodes)
        
        # 使用基础向量存储添加节点
        return self.base_vector_store.add(enhanced_nodes, **kwargs)
    
    def query(self, query_embedding: List[float], similarity_top_k: int = 10, **kwargs) -> List[NodeWithScore]:
        """查询向量存储"""
        # 使用基础向量存储查询
        results = self.base_vector_store.query(
            query_embedding=query_embedding,
            similarity_top_k=similarity_top_k,
            **kwargs
        )
        
        # 增强检索结果
        query_str = kwargs.get("query_str", "")
        enhanced_results = self.enhancer.enhance_retrieval_results(results, query_str)
        
        return enhanced_results
    
    def delete(self, ref_doc_id: str, **kwargs) -> None:
        """删除文档"""
        # 清理缓存
        nodes_to_remove = [
            node_id for node_id, info in self.enhancer.table_node_cache.items()
            if info["metadata"].get("ref_doc_id") == ref_doc_id
        ]
        
        for node_id in nodes_to_remove:
            del self.enhancer.table_node_cache[node_id]
        
        # 使用基础向量存储删除
        return self.base_vector_store.delete(ref_doc_id, **kwargs)
    
    def __getattr__(self, name):
        """代理其他方法到基础向量存储"""
        return getattr(self.base_vector_store, name)


def create_tablerag_vector_enhancer(embedding_model: BaseEmbedding) -> TableRAGVectorEnhancer:
    """创建TableRAG向量增强器"""
    return TableRAGVectorEnhancer(embedding_model)


def wrap_vector_store_with_tablerag(
    vector_store: VectorStore, 
    embedding_model: BaseEmbedding
) -> TableRAGAwareVectorStore:
    """用TableRAG功能包装向量存储"""
    enhancer = create_tablerag_vector_enhancer(embedding_model)
    return TableRAGAwareVectorStore(vector_store, enhancer)


# 示例用法
if __name__ == "__main__":
    print("TableRAG向量增强器模块")
    
    # 这里可以添加测试代码
    from llama_index.embeddings.huggingface import HuggingFaceEmbedding
    
    # 创建示例嵌入模型
    embed_model = HuggingFaceEmbedding(model_name="BAAI/bge-large-zh-v1.5")
    
    # 创建增强器
    enhancer = create_tablerag_vector_enhancer(embed_model)
    print("TableRAG向量增强器创建成功")
