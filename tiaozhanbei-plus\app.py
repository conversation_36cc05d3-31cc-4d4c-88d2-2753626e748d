import torch
import json
import time
from pathlib import Path
from typing import List, Dict
import re
import chromadb
import streamlit as st
from llama_index.core import VectorStoreIndex, StorageContext, Settings, get_response_synthesizer
from llama_index.core.chat_engine import ContextChatEngine
from llama_index.core.memory import Chat<PERSON><PERSON>ory<PERSON><PERSON>er
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.schema import TextNode, Document
from llama_index.llms.huggingface import HuggingFaceLLM
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.core import PromptTemplate
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.llms.openai_like import OpenAILike
from llama_index.core import get_response_synthesizer
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.retrievers.bm25 import BM25Retriever
from llama_index.core.retrievers import QueryFusionRetriever
import base64
import io
import nest_asyncio
import numpy as np
from pypdf import PdfReader
# from pdf2image import convert_from_path
# import pytesseract
from pathlib import Path
from typing import List, Dict
# from mineru import miner
from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.enum_class import MakeMode
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
import copy
import pandas as pd
from llama_index.experimental.query_engine import PandasQueryEngine
from llama_index.core.tools import QueryEngineTool
from llama_index.core.selectors import LLMSingleSelector
from llama_index.core.query_engine import RouterQueryEngine
import concurrent.futures
from functools import lru_cache

# 导入TableRAG适配器
try:
    from tablerag_adapter import TableRAGAdapter, create_tablerag_adapter, process_excel_with_tablerag
    from tablerag_config import get_tablerag_config, TableRAGIntegrationConfig
    from tablerag_retriever import (
        create_tablerag_enhanced_retriever,
        create_tablerag_fusion_retriever,
        TableRAGEnhancedRetriever
    )
    from tablerag_vector_enhancer import (
        create_tablerag_vector_enhancer,
        wrap_vector_store_with_tablerag
    )
    TABLERAG_AVAILABLE = True
    print("TableRAG适配器导入成功")
except ImportError as e:
    print(f"TableRAG适配器导入失败: {e}")
    TABLERAG_AVAILABLE = False

# 应用nest_asyncio补丁，以在Streamlit等现有事件循环中运行异步LlamaIndex代码
nest_asyncio.apply()

# ================== Streamlit页面配置 ==================
st.set_page_config(
    page_title="南水北调水利问答助手",
    page_icon="assets/Picture/lixiahe.png",
    layout="centered",
    initial_sidebar_state="auto"
)


# 新增: 加载自定义CSS文件的函数
def load_css(file_path):
    try:
        with open(file_path, encoding='utf-8') as f:
            st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS文件未找到: {file_path}")


def disable_streamlit_watcher():
    """Patch Streamlit to disable file watcher"""

    def _on_script_changed(_):
        return

    from streamlit import runtime
    runtime.get_instance()._on_script_changed = _on_script_changed


# 新增：生成文本文件的函数
def generate_single_qa_text(question, answer):
    """生成单次问答的文本文件，完全支持中文"""
    content = "南水北调水利问答\n\n"
    content += f"问题:\n{question}\n\n"
    content += f"回答:\n{answer}\n\n"
    content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"

    return content.encode('utf-8')


# ================================ 配置类 ================================
class Config:
    EMBED_MODEL_PATH = r"D:\pythonProject\embedding_model\BAAI\bge-large-zh-v1___5"
    RERANK_MODEL_PATH = r"D:\pythonProject\llms\BAAI\bge-reranker-base"  # 新增重排序模型路径

    DATA_DIR = "./data"
    VECTOR_DB_DIR = "./chroma_db"
    PERSIST_DIR = "./storage"

    COLLECTION_NAME = "chinese_labor_laws"
    TOP_K = 10  # 扩大初筛范围，检索更多潜在相关的文档
    RERANK_TOP_K = 3  # 严格重排序，只保留最相关的3个文档，提高信噪比
    
    # 相关度过滤配置
    RELEVANCE_THRESHOLD = 0.3  # 相关度阈值，低于此值的文档将被过滤掉
    
    # 文本分块配置
    # 可选值: 'semantic_double_merge' (语义双重合并分块), 'semantic' (语义分块), 'sentence' (传统句子分块)
    CHUNKING_MODE = 'semantic'
    # 传统分块参数
    CHUNK_SIZE = 512
    CHUNK_OVERLAP = 300
    # 语义分块参数
    SEMANTIC_BREAKPOINT_THRESHOLD = 90  # 百分位数阈值，越低生成的节点越多
    # 语义双重合并分块参数
    SEMANTIC_DOUBLE_INITIAL_THRESHOLD = 0.4  # 初始分块阈值
    SEMANTIC_DOUBLE_APPENDING_THRESHOLD = 0.5  # 附加阈值
    SEMANTIC_DOUBLE_MERGING_THRESHOLD = 0.5  # 合并阈值
    SEMANTIC_DOUBLE_MAX_CHUNK_SIZE = 3000  # 最大块大小

    # TableRAG配置 - 6GB显存优化版本
    USE_TABLERAG = TABLERAG_AVAILABLE  # 是否启用TableRAG
    TABLERAG_CONFIG_NAME = "low_memory"  # 使用低内存配置
    EXCEL_PROCESSING_MODE = "simple"  # 优先使用简单模式，减少显存占用

    # 6GB显存优化参数
    LOW_MEMORY_MODE = True  # 启用低内存模式
    MAX_BATCH_SIZE = 1  # 减小批处理大小
    ENABLE_MODEL_OFFLOAD = True  # 启用模型卸载


# ================== Pandas输出处理器 ==================
def custom_output_processor(output, query_str):
    """智能处理Pandas输出，确保生成有效的Python代码"""
    import re
    import ast

    # 如果输出为空，提供数据探索建议
    if not output or output.isspace():
        return "print('查询结果为空。'); print(f'数据形状: {df.shape}'); print(df.head())"

    # 清理markdown代码块标记
    cleaned_output = output.strip()

    # 移除markdown代码块标记
    cleaned_output = re.sub(r'```python\s*\n?', '', cleaned_output)
    cleaned_output = re.sub(r'```\s*$', '', cleaned_output)
    cleaned_output = re.sub(r'^```\s*\n?', '', cleaned_output)

    # 移除多余的解释文字，只保留Python代码
    lines = cleaned_output.split('\n')
    code_lines = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 跳过纯文本解释
        if (line.startswith('根据') or line.startswith('如果') or
            line.startswith('您是否') or line.startswith('1.') or
            line.startswith('2.') or line.startswith('3.') or
            line.startswith('如果数据中没有') or
            ('分析' in line and not line.startswith('#') and not '=' in line)):
            continue

        # 保留Python代码行
        if (line.startswith('#') or line.startswith('import') or
            line.startswith('df') or line.startswith('print') or
            line.startswith('try:') or line.startswith('except') or
            '=' in line or line.startswith('if ') or
            line.startswith('else:') or line.startswith('for ') or
            line.startswith('    ')):  # 缩进的代码行
            code_lines.append(line)

    # 重新组合代码
    if code_lines:
        final_code = '\n'.join(code_lines)

        # 验证代码语法
        try:
            ast.parse(final_code)
            return final_code
        except SyntaxError:
            # 如果语法有问题，使用简化版本
            pass

    # 检查是否为错误消息或无有效代码，返回数据探索代码
    error_keywords = [
        "No matching data found",
        "找不到符合条件的结果",
        "没有找到",
        "不存在",
        "无结果"
    ]

    for keyword in error_keywords:
        if keyword in output:
            return "print('未找到精确匹配的结果。'); print(f'数据概况 - 总行数: {len(df)}'); print(f'列名: {df.columns.tolist()}'); print('前5行数据:'); print(df.head())"

    # 默认返回数据探索代码
    return "print('数据基本信息:'); print(f'数据形状: {df.shape}'); print(f'列名: {df.columns.tolist()}'); print('前5行数据:'); print(df.head())"


# ================== 缓存资源初始化 ==================
@st.cache_resource(show_spinner="初始化模型中...")
def init_models():
    # 文本嵌入模型
    embed_model = HuggingFaceEmbedding(
        model_name=Config.EMBED_MODEL_PATH,
        device='cuda' if torch.cuda.is_available() else 'cpu'  # 指定设备
    )

    # 使用DeepSeek的OpenAI兼容API
    llm = OpenAILike(
        model="deepseek-chat",  # 可选模型：glm-4, glm-3-turbo, characterglm等
        api_base="https://api.deepseek.com",  # 关键！必须指定此端点
        api_key="***********************************",
        context_window=128000,  # 按需调整（glm-4实际支持128K）
        is_chat_model=True,
        is_function_calling_model=False,  # GLM暂不支持函数调用
        max_tokens=4096,  # 最大生成token数（按需调整）
        temperature=0.3,  # 推荐范围 0.1~1.0
        top_p=0.7  # 推荐范围 0.5~1.0
    )

    # 重排序模型
    reranker = SentenceTransformerRerank(
        model=Config.RERANK_MODEL_PATH,
        top_n=Config.RERANK_TOP_K
    )

    Settings.embed_model = embed_model
    Settings.llm = llm

    return embed_model, llm, reranker


# 新增：缓存NodeParser
@st.cache_resource(show_spinner="初始化文本分割器...")
def init_node_parser():
    """
    初始化语义感知文本分割器，根据内容语义自动调整块大小，避免语义割裂
    支持三种分块方式:
    1. 语义双重合并分块 (最先进，需要spaCy)
    2. 语义分块 (较先进)
    3. 传统句子分块 (回退方案)
    """
    # 从配置中获取分块模式
    chunking_mode = Config.CHUNKING_MODE
    
    try:
        if chunking_mode == 'semantic_double_merge':
            # 尝试导入语义双重合并分块所需的模块
            try:
                import spacy
                from llama_index.core.node_parser import (
                    SemanticDoubleMergingSplitterNodeParser,
                    LanguageConfig
                )
                
                # 检查是否已安装spaCy模型
                try:
                    nlp = spacy.load("zh_core_web_md")
                    print("成功加载中文spaCy模型")
                except OSError:
                    print("未找到中文spaCy模型，尝试使用英文模型")
                    try:
                        nlp = spacy.load("en_core_web_md")
                        print("成功加载英文spaCy模型")
                    except OSError:
                        raise ImportError("未找到所需的spaCy模型，请安装: python -m spacy download zh_core_web_md")
                
                # 配置语言设置
                language = "chinese" if "zh" in nlp.meta["lang"] else "english"
                config = LanguageConfig(
                    language=language,
                    spacy_model=nlp.meta["name"]
                )
                
                # 创建语义双重合并分块器，使用配置参数
                semantic_double_merger = SemanticDoubleMergingSplitterNodeParser(
                    language_config=config,
                    initial_threshold=Config.SEMANTIC_DOUBLE_INITIAL_THRESHOLD,
                    appending_threshold=Config.SEMANTIC_DOUBLE_APPENDING_THRESHOLD,
                    merging_threshold=Config.SEMANTIC_DOUBLE_MERGING_THRESHOLD,
                    max_chunk_size=Config.SEMANTIC_DOUBLE_MAX_CHUNK_SIZE
                )
                
                print(f"成功初始化语义双重合并分块器 (语言: {language})")
                return semantic_double_merger
                
            except (ImportError, Exception) as e:
                print(f"语义双重合并分块器初始化失败: {str(e)}，回退到语义分块器")
                chunking_mode = 'semantic'
        
        if chunking_mode == 'semantic':
            # 尝试导入语义分块所需的模块
            from llama_index.core.node_parser import SemanticSplitterNodeParser
            from llama_index.embeddings.huggingface import HuggingFaceEmbedding
            
            # 使用与系统相同的嵌入模型，保持一致性
            embed_model = HuggingFaceEmbedding(
                model_name=Config.EMBED_MODEL_PATH,
                device='cuda' if torch.cuda.is_available() else 'cpu'
            )
            
            # 创建语义分块器，使用配置参数
            semantic_splitter = SemanticSplitterNodeParser(
                buffer_size=1,  # 每次分析一个句子
                breakpoint_percentile_threshold=Config.SEMANTIC_BREAKPOINT_THRESHOLD,
                embed_model=embed_model
            )
            
            print("成功初始化语义分块器")
            return semantic_splitter
            
    except (ImportError, Exception) as e:
        print(f"语义分块器初始化失败: {str(e)}，回退到传统分块器")
    
    # 如果语义分块器初始化失败，回退到传统的SentenceSplitter
    from llama_index.core.node_parser import SentenceSplitter
    print("使用传统句子分块器")
    return SentenceSplitter(
        chunk_size=Config.CHUNK_SIZE,
        chunk_overlap=Config.CHUNK_OVERLAP
    )


@st.cache_resource(show_spinner="加载知识库中...")
def load_index():
    """
    根据官方文档推荐的最佳实践，从磁盘显式加载知识库的各个组件。
    """
    from llama_index.core.storage.docstore import SimpleDocumentStore
    persist_dir = Path(Config.PERSIST_DIR)
    db_dir = Path(Config.VECTOR_DB_DIR)
    docstore_path = persist_dir / "docstore.json"

    if not all([persist_dir.exists(), db_dir.exists(), docstore_path.exists()]):
        print("知识库目录或必要文件(docstore.json)不存在。")
        return None
    
    try:
        print("--- 正在加载知识库 ---")
        # 1. 显式加载向量数据库
        db = chromadb.PersistentClient(path=str(db_dir))
        chroma_collection = db.get_or_create_collection(Config.COLLECTION_NAME)
        base_vector_store = ChromaVectorStore(chroma_collection=chroma_collection)

        # 2. 如果启用TableRAG，包装向量存储
        if Config.USE_TABLERAG and TABLERAG_AVAILABLE:
            try:
                embed_model, _, _ = init_models()
                vector_store = wrap_vector_store_with_tablerag(base_vector_store, embed_model)
                print("TableRAG向量存储增强器初始化成功")
            except Exception as e:
                print(f"TableRAG向量存储增强器初始化失败: {e}")
                vector_store = base_vector_store
        else:
            vector_store = base_vector_store

        # 3. 显式加载文档库
        docstore = SimpleDocumentStore.from_persist_path(str(docstore_path))
        print(f"文档库加载成功，共 {len(docstore.docs)} 个文档。")

        # 4. 从加载的组件重建存储上下文和索引
        storage_context = StorageContext.from_defaults(
            docstore=docstore, vector_store=vector_store
        )
        # 修复：使用官方推荐的构造函数，而不是废弃的 from_storage
        index = VectorStoreIndex(nodes=[], storage_context=storage_context)
        
        print("--- 知识库加载成功！---")
        return index
    except Exception as e:
        print(f"加载知识库失败: {e}")
        import traceback
        traceback.print_exc()
        return None


# ============================== 数据处理 ==============================
# ===========数据处理json格式数据 ==========
def load_and_validate_json_files(data_dir: str) -> List[Dict]:
    """加载并验证JSON法律文件"""
    json_files = list(Path(data_dir).glob("*.json"))
    assert json_files, f"未找到JSON文件于 {data_dir}"

    all_data = []
    for json_file in json_files:
        with open(json_file, 'r', encoding='utf-8') as f:
            try:
                data = json.load(f)
                # 验证数据结构
                if not isinstance(data, list):
                    raise ValueError(f"文件 {json_file.name} 根元素应为列表")
                for item in data:
                    if not isinstance(item, dict):
                        raise ValueError(f"文件 {json_file.name} 包含非字典元素")
                    for k, v in item.items():
                        if not isinstance(v, str):
                            raise ValueError(f"文件 {json_file.name} 中键 '{k}' 的值不是字符串")
                all_data.extend({
                                    "content": item,
                                    "metadata": {"source_file": json_file.name, "content_type": "json_item"}
                                } for item in data)
            except Exception as e:
                raise RuntimeError(f"加载文件 {json_file} 失败: {str(e)}")

    print(f"成功加载 {len(all_data)} 个法律文件条目")
    return all_data

# ===========数据处理PDF格式数据 ==========
# 设置 Tesseract 路径（Windows 专用）
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
def load_pdfs(data_dir: str) -> List[Dict]:
    pdf_files = list(Path(data_dir).rglob("*.pdf"))
    all_data = []
    skipped_files = 0

    for pdf_file in pdf_files:
        try:
            # 使用MinerU进行PDF解析
            pdf_bytes = read_fn(pdf_file)
            pdf_bytes_list = [pdf_bytes]
            p_lang_list = ['ch']

            # 1. 分析文档
            infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(
                pdf_bytes_list, p_lang_list, parse_method="auto", formula_enable=True, table_enable=True
            )
            
            # 假设我们只处理单个文件
            model_list = infer_results[0]
            images_list = all_image_lists[0]
            pdf_doc = all_pdf_docs[0]
            _lang = lang_list[0]
            _ocr_enable = ocr_enabled_list[0]

            # 2. 转换为中间JSON格式 (在内存中操作，不写入文件)
            class InMemoryWriter:
                def write(self, *args, **kwargs): pass
                def write_string(self, *args, **kwargs): pass

            middle_json = pipeline_result_to_middle_json(
                model_list, images_list, pdf_doc, InMemoryWriter(), _lang, _ocr_enable
            )
            
            pdf_info = middle_json["pdf_info"]
            
            # 3. 从中间格式生成Markdown文本
            text = pipeline_union_make(pdf_info, MakeMode.MM_MD, "images")

            if not text.strip() or len(text.strip()) < 20:
                print(f"警告：MinerU解析后PDF文件 {pdf_file.name} 内容过少或为空，已跳过。")
                skipped_files += 1
                continue

            all_data.append({
                "content": text,
                "metadata": {
                    "source_file": str(pdf_file.relative_to(data_dir)),
                    "content_type": "pdf_document"
                }
            })
        except Exception as e:
            print(f"MinerU处理PDF文件 {pdf_file.name} 时出错: {str(e)}")
            skipped_files += 1

    print(f"MinerU成功加载 {len(all_data)} 个 PDF 文件")
    if skipped_files > 0:
        print(f"有 {skipped_files} 个PDF文件因解析问题被跳过")

    return all_data


# ===========数据处理excel格式数据 ==========
import pandas as pd
from pathlib import Path
import concurrent.futures
from functools import lru_cache

def load_excels_enhanced(data_dir: str) -> List[Dict]:
    """
    增强的Excel加载函数，支持TableRAG处理
    根据配置选择处理模式：simple, tablerag, hybrid
    """
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    if not excel_files:
        print("未发现Excel文件，跳过加载。")
        return []

    processing_mode = Config.EXCEL_PROCESSING_MODE
    print(f"Excel处理模式: {processing_mode}")

    if processing_mode == "tablerag" and Config.USE_TABLERAG:
        return _load_excels_with_tablerag(data_dir)
    elif processing_mode == "hybrid" and Config.USE_TABLERAG:
        return _load_excels_hybrid(data_dir)
    else:
        return _load_excels_simple(data_dir)


def _load_excels_with_tablerag(data_dir: str) -> List[Dict]:
    """使用TableRAG处理Excel文件"""
    try:
        # 获取TableRAG配置
        tablerag_config = get_tablerag_config(Config.TABLERAG_CONFIG_NAME)

        # 创建TableRAG适配器
        adapter = create_tablerag_adapter(tablerag_config.to_dict())

        # 处理Excel文件
        processed_data = adapter.process_excel_files(data_dir)

        print(f"TableRAG处理完成，共生成 {len(processed_data)} 个数据条目")
        return processed_data

    except Exception as e:
        print(f"TableRAG处理失败: {e}")
        print("回退到简单处理模式")
        return _load_excels_simple(data_dir)


def _load_excels_hybrid(data_dir: str) -> List[Dict]:
    """混合模式：同时使用TableRAG和简单处理"""
    all_data = []

    # 首先尝试TableRAG处理
    try:
        tablerag_data = _load_excels_with_tablerag(data_dir)
        all_data.extend(tablerag_data)
        print(f"TableRAG处理生成 {len(tablerag_data)} 个条目")
    except Exception as e:
        print(f"TableRAG处理失败: {e}")

    # 然后添加简单处理的结果
    try:
        simple_data = _load_excels_simple(data_dir)
        # 为简单处理的数据添加标识
        for item in simple_data:
            item["metadata"]["processing_method"] = "simple"
        all_data.extend(simple_data)
        print(f"简单处理生成 {len(simple_data)} 个条目")
    except Exception as e:
        print(f"简单处理失败: {e}")

    print(f"混合模式处理完成，总共 {len(all_data)} 个条目")
    return all_data


def _load_excels_simple(data_dir: str) -> List[Dict]:
    """
    简单的Excel加载函数（原始方法）
    1. 使用并行处理加速多文件处理
    2. 使用LRU缓存减少重复计算
    3. 批量处理数据以提高效率
    4. 智能判断数据类型减少转换错误
    """
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    if not excel_files:
        print("未发现Excel文件，跳过加载。")
        return []
    all_data = []
    
    # 定义日期格式的缓存函数
    @lru_cache(maxsize=128)
    def format_datetime(value_str):
        try:
            dt = pd.to_datetime(value_str, errors='coerce')
            if pd.notna(dt):
                return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            pass
        return value_str
    
    # 智能处理单个值
    def process_value(col, value_str):
        # 如果值为空，直接返回"未知"
        if pd.isna(value_str) or value_str == "":
            return "未知"
            
        # 处理日期时间类型
        if isinstance(value_str, str) and ('时间' in str(col) or '日期' in str(col)):
            return format_datetime(value_str)
            
        # 处理数值类型
        if isinstance(value_str, str):
            try:
                value_float = float(value_str)
                # 只对非整数进行四舍五入
                if value_float.is_integer():
                    return str(int(value_float))
                else:
                    return str(round(value_float, 3))
            except (ValueError, TypeError):
                pass
                
        return str(value_str)
    
    # 处理单个Excel文件
    def process_excel_file(excel_file):
        file_data = []
        try:
            # 使用engine='openpyxl'提高与新版Excel的兼容性
            sheets = pd.read_excel(excel_file, sheet_name=None, dtype=str, engine='openpyxl')
            
            for sheet_name, df in sheets.items():
                if df.empty:
                    continue
                
                # 清理列名
                df.columns = df.columns.map(str)
                columns = df.columns.tolist()
                
                # 预处理整个数据框，而不是逐行处理
                # 优化1: 批量处理时间列
                time_cols = [col for col in columns if '时间' in str(col) or '日期' in str(col)]
                for col in time_cols:
                    if col in df.columns:
                        df[col] = df[col].apply(lambda x: format_datetime(x) if pd.notna(x) and isinstance(x, str) else x)
                
                # 处理每行数据
                for idx, row in df.iterrows():
                    # 构建键值对列表
                    parts = [f"'{col}': '{process_value(col, row[col])}'" for col in columns]
                    
                    # 采用结构化文本格式
                    row_description = f"数据记录 - 文件: {excel_file.name}, 工作表: {sheet_name}, 行号: {idx + 2}. 内容: {{{', '.join(parts)}}}"
                    
                    file_data.append({
                        "content": row_description,
                        "metadata": {
                            "source_file": str(excel_file.relative_to(data_dir)),
                            "content_type": "excel_row",
                            "sheet_name": sheet_name,
                            "row_number": idx + 2
                        }
                    })
                    
        except Exception as e:
            print(f"加载或处理Excel文件 {excel_file} 时出错: {str(e)}")
        return file_data
    
    # 并行处理所有Excel文件
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(8, len(excel_files))) as executor:
        results = list(executor.map(process_excel_file, excel_files))
    
    # 合并所有结果
    for result in results:
        all_data.extend(result)
    
    print(f"成功从Excel文件中加载并处理了 {len(all_data)} 行数据。")
    return all_data


# 新增：加载Excel为DataFrame字典
@st.cache_resource(show_spinner="加载Excel表格数据中...")
def load_excel_dataframes(data_dir: str) -> Dict[str, pd.DataFrame]:
    """将./data目录下的每个Excel文件的每个工作表加载为一个Pandas DataFrame。"""
    excel_files = list(Path(data_dir).rglob("*.xlsx"))
    all_dfs = {}
    if not excel_files:
        print("未发现Excel文件。")
        return all_dfs

    for file_path in excel_files:
        try:
            # 读取所有工作表
            sheets = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            for sheet_name, df in sheets.items():
                if not df.empty:
                    # 使用文件名和工作表名创建唯一键
                    key_name = f"{file_path.stem}_{sheet_name}"
                    all_dfs[key_name] = df
                    print(f"成功加载 '{file_path.name}' 的工作表 '{sheet_name}' (共 {len(df)} 行)")
        except Exception as e:
            print(f"加载Excel文件 {file_path.name} 时出错: {e}")
    
    print(f"共加载了 {len(all_dfs)} 个工作表作为DataFrame。")
    return all_dfs


import os
def create_nodes_from_text(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    for entry in raw_data:
        content = entry["content"]
        source_file = entry["metadata"].get("source_file", "unknown_source")

        doc = Document(text=content, metadata={"source_file": source_file, "content_type": "text_document"})
        nodes = node_parser.get_nodes_from_documents([doc])

        for i, node in enumerate(nodes):
            node.id_ = f"{source_file}::chunk_{i}"
            node.metadata.update({
                "source_file": source_file,
                "content_type": "text_document_chunk"
            })
            all_nodes.append(node)

    return all_nodes


# ===========专门处理PDF文本的节点生成函数 ==========

# 添加专门处理PDF文件的节点生成函数
def create_nodes_from_pdf(raw_data: List[Dict], node_parser) -> List[TextNode]:
    all_nodes = []
    warning_count = 0

    for entry in raw_data:
        content = entry["content"]
        if not content or len(content.strip()) < 10:
            source = entry["metadata"].get("source_file", "未知文件")
            print(f"警告：PDF文件 {source} 内容为空，已跳过")
            warning_count += 1
            continue

        source_file = entry["metadata"]["source_file"]

        doc = Document(text=content, metadata={
            "source_file": source_file,
            "content_type": "pdf_document"
        })

        try:
            nodes = node_parser.get_nodes_from_documents([doc])
            for i, node in enumerate(nodes):
                node.id_ = f"{source_file}::chunk_{i}"
                node.metadata.update({
                    "source_file": source_file,
                    "content_type": "pdf_document_chunk"
                })
                all_nodes.append(node)
        except Exception as e:
            print(f"处理PDF文件 {source_file} 时出错: {str(e)}")

    if warning_count > 0:
        print(f"注意: {warning_count} 个PDF文件因内容为空被跳过")

    print(f"成功从PDF内容生成 {len(all_nodes)} 个文本节点。")
    return all_nodes
def create_nodes_from_excel(raw_data: List[Dict], node_parser) -> List[TextNode]:
    """
    为Excel数据创建TextNode。
    支持TableRAG增强数据和传统行级数据。
    优化处理以更高效地创建节点并保持唯一ID。
    支持并行处理以加快处理速度。
    """
    if not raw_data:
        print("警告: 没有Excel数据可以处理")
        return []

    all_nodes = []

    # 分别处理不同类型的Excel数据
    excel_entries = []
    tablerag_entries = []

    for entry in raw_data:
        metadata = entry.get("metadata", {})
        content_type = metadata.get("content_type", "")
        processing_method = metadata.get("processing_method", "")

        if content_type == "excel_row":
            excel_entries.append(entry)
        elif content_type == "excel_table" or processing_method == "tablerag":
            tablerag_entries.append(entry)
        else:
            # 其他类型的Excel数据
            excel_entries.append(entry)
    
    # 处理传统Excel行级数据
    if excel_entries:
        # 按文件和工作表分组，以便更好地组织节点
        file_sheet_groups = {}
        for entry in excel_entries:
            metadata = entry["metadata"]
            file_key = metadata.get("source_file", "unknown_file")
            sheet_key = metadata.get("sheet_name", "unknown_sheet")
            key = (file_key, sheet_key)

            if key not in file_sheet_groups:
                file_sheet_groups[key] = []
            file_sheet_groups[key].append(entry)

        # 处理每个文件-工作表组
        for (file_key, sheet_key), entries in file_sheet_groups.items():
            # 为每个组创建统一的前缀
            prefix = f"{file_key}::{sheet_key}"

            # 创建节点
            for entry in entries:
                metadata = entry["metadata"]
                row_number = metadata.get("row_number", "unknown_row")
                node_id = f"{prefix}::row_{row_number}"

                node = TextNode(
                    text=entry["content"],
                    id_=node_id,
                    metadata=metadata
                )
                all_nodes.append(node)

    # 处理TableRAG增强数据
    if tablerag_entries:
        for i, entry in enumerate(tablerag_entries):
            metadata = entry["metadata"]
            file_key = metadata.get("source_file", "unknown_file")
            sheet_key = metadata.get("sheet_name", "unknown_sheet")
            processing_method = metadata.get("processing_method", "tablerag")

            # 为TableRAG数据创建特殊的节点ID
            node_id = f"tablerag::{file_key}::{sheet_key}::{i}"

            # 使用增强的内容
            content = entry["content"]

            # 添加TableRAG特有的元数据
            enhanced_metadata = {
                **metadata,
                "node_type": "tablerag_enhanced",
                "has_enhancements": bool(entry.get("enhancements")),
                "table_format": metadata.get("table_format", "unknown")
            }

            # 如果有原始表格数据，也添加到元数据中
            if "raw_table" in entry:
                enhanced_metadata["raw_table_preview"] = entry["raw_table"][:200] + "..." if len(entry["raw_table"]) > 200 else entry["raw_table"]

            node = TextNode(
                text=content,
                id_=node_id,
                metadata=enhanced_metadata
            )
            all_nodes.append(node)

    print(f"从Excel数据中创建了 {len(all_nodes)} 个文本节点")
    print(f"  - 传统Excel节点: {len(excel_entries)}")
    print(f"  - TableRAG增强节点: {len(tablerag_entries)}")
    return all_nodes


# ================== 界面组件 ==================
# 修改: 使用自定义的HTML/CSS渲染聊天气泡
def display_chat_message(message):
    """根据角色渲染用户或助手的聊天气泡"""
    role = message["role"]
    content = message.get("cleaned", message["content"])

    if role == "user":
        st.markdown(f'<div class="user-bubble">{content}</div>', unsafe_allow_html=True)
    elif role == "assistant":
        st.markdown(f'<div class="assistant-bubble">{content}</div>', unsafe_allow_html=True)

        # 将思考过程和参考文献的展开框放在气泡下方
        if message.get("think"):
            with st.expander("📝 查看模型思考过程"):
                for think_content in message["think"]:
                    st.info(think_content.strip())

        if "reference_nodes" in message and message["reference_nodes"]:
            show_reference_details(message["reference_nodes"])

    # 添加一个清除浮动的div，防止布局错乱
    st.markdown('<div style="clear: both;"></div>', unsafe_allow_html=True)


def init_chat_interface():
    if "messages" not in st.session_state:
        st.session_state.messages = []

    for msg in st.session_state.messages:
        role = msg["role"]
        content = msg.get("cleaned", msg["content"])  # 优先使用清理后的内容

        with st.chat_message(role):
            st.markdown(content)

            # 如果是助手消息且包含思维链
            if role == "assistant" and msg.get("think"):
                with st.expander("📝 模型思考过程（历史对话）"):
                    for think_content in msg["think"]:
                        st.markdown(f'<span style="color: #808080">{think_content.strip()}</span>',
                                    unsafe_allow_html=True)

            # 如果是助手消息且有参考依据（需要保持原有参考依据逻辑）
            if role == "assistant" and "reference_nodes" in msg:
                show_reference_details(msg["reference_nodes"])


# 修改: 优化参考文献的显示样式
def show_reference_details(nodes):
    with st.expander("📚 查看参考文献"):
        for idx, node in enumerate(nodes, 1):
            meta = node.node.metadata
            source_file = meta.get("source_file", "未知文件")
            
            # 优先使用高亮后的文本，如果不存在则回退到原始文本
            content_to_display = meta.get("highlighted_text", node.node.text)

            st.markdown(f"""
            <div class="reference-expander">
                <p><b>[{idx}] 来源:</b> {source_file}</p>
                <p><b>相关度:</b> {node.score:.4f}</p>
                <p><b>内容片段:</b></p>
                <blockquote>{content_to_display}</blockquote>
            </div>
            """, unsafe_allow_html=True)


# 新增：高亮显示与答案最相关的句子
def highlight_relevant_sentences(answer: str, source_nodes: list, reranker_model) -> list:
    """
    在源节点文本中高亮显示与答案最相关的句子。
    """
    if not source_nodes or not answer:
        return source_nodes

    for node_with_score in source_nodes:
        node = node_with_score.node
        
        # 1. 将文本分割成句子（保留分隔符）
        sentences = re.split(r'([。！？\n.?!])', node.text)
        if not sentences:
            continue
        
        # 将句子和分隔符重新组合
        combined_sentences = []
        for i in range(0, len(sentences) - 1, 2):
            combined_sentences.append(sentences[i] + sentences[i+1])
        if len(sentences) % 2 == 1:
            combined_sentences.append(sentences[-1])
        
        # 过滤掉空句子
        combined_sentences = [s.strip() for s in combined_sentences if s and s.strip()]
        if not combined_sentences:
            node.metadata["highlighted_text"] = node.text
            continue

        # 2. 计算每个句子与答案的相关度分数
        pairs = [[answer, s] for s in combined_sentences]
        try:
            scores = reranker_model.predict(pairs)
        except Exception as e:
            print(f"高亮函数计算分数时出错: {e}")
            node.metadata["highlighted_text"] = node.text
            continue

        # 3. 找到分数最高的句子并高亮
        if isinstance(scores, np.ndarray):
            best_sentence_idx = np.argmax(scores)
            highlighted_sentence = f"**{combined_sentences[best_sentence_idx]}**"
            combined_sentences[best_sentence_idx] = highlighted_sentence
            
            # 4. 重新组合文本并存入元数据
            highlighted_text = " ".join(combined_sentences)
            node.metadata["highlighted_text"] = highlighted_text
        else:
            # 如果分数返回格式不正确，则回退
            node.metadata["highlighted_text"] = node.text

    return source_nodes


# 新增：记录系统无法回答的问题
def log_unanswerable_question(question: str, context_nodes=None):
    """
    记录系统无法回答的问题，以便后续改进知识库
    
    Args:
        question: 用户提问的问题
        context_nodes: 检索到的上下文节点（如果有）
    """
    try:
        log_dir = Path("./logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / "unanswerable_questions.jsonl"
        
        # 准备日志条目
        log_entry = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "question": question,
            "retrieved_contexts": []
        }
        
        # 如果有上下文节点，记录它们的信息
        if context_nodes:
            for node in context_nodes:
                log_entry["retrieved_contexts"].append({
                    "text": node.node.text[:200] + "..." if len(node.node.text) > 200 else node.node.text,
                    "source": node.node.metadata.get("source_file", "未知来源"),
                    "score": node.score if hasattr(node, "score") else None
                })
        
        # 追加到日志文件
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")
            
        print(f"已记录无法回答的问题: {question}")
    except Exception as e:
        print(f"记录无法回答的问题时出错: {str(e)}")


# 新增：重新添加被误删的相关度过滤器类
class RelevanceScoreNodePostprocessor:
    """过滤掉相关度低于阈值的节点"""
    def __init__(self, threshold=0.3):
        self.threshold = threshold
        
    def postprocess_nodes(self, nodes, query_bundle):
        # 过滤掉相关度低于阈值的节点
        filtered_nodes = [node for node in nodes if node.score >= self.threshold]
        
        # 如果过滤后没有节点，返回一个空列表
        if not filtered_nodes and nodes:
            print(f"警告：所有检索到的节点相关度都低于阈值 {self.threshold}，将返回空结果")
        
        return filtered_nodes


# ================== 主程序 ==================
def main():
    load_css("frontend/style.css")

    # 初始化按钮计数器
    if "button_counter" not in st.session_state:
        st.session_state.button_counter = 0

    # 确保messages已初始化
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # --- 侧边栏 ---
    with st.sidebar:
        st.image("assets/Picture/lixiahe.png", width=80)
        st.title("控制与信息")
        st.info("欢迎使用南水北调水利问答助手。本系统旨在提供专业、准确的信息。")

        # 清除聊天记录按钮 - 添加唯一key
        if st.button("清除聊天记录", use_container_width=True, type="primary", key="clear_chat_button"):
            st.session_state.messages = []
            # 同时清除后端的对话内存
            if "chat_engine" in st.session_state:
                st.session_state.chat_engine.reset()
            st.rerun()

        # 导出整个对话为文本文件 - 添加唯一key
        if st.session_state.messages and st.button("导出对话为文本", use_container_width=True,
                                                   key="export_all_text_button"):
            try:
                content = "南水北调水利问答记录\n\n"

                for msg in st.session_state.messages:
                    role = msg["role"]
                    text = msg.get("cleaned", msg["content"])

                    if role == "user":
                        content += f"问题:\n{text}\n\n"
                    elif role == "assistant":
                        content += f"回答:\n{text}\n\n"

                content += f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                text_bytes = content.encode('utf-8')

                timestamp = time.strftime("%Y%m%d-%H%M%S")
                b64 = base64.b64encode(text_bytes).decode()
                href = f'<a href="data:text/plain;charset=utf-8;base64,{b64}" download="南水北调问答-{timestamp}.txt">点击下载文本文件</a>'
                st.markdown(href, unsafe_allow_html=True)
            except Exception as e:
                st.error(f"生成文本时出错: {str(e)}")

    # --- 主界面 ---
    st.title("南水北调水利问答助手 💧")
    st.markdown("请输入您的问题，我们将基于最新的研究成果和工程实践为您解答。")

    embed_model, llm, reranker = init_models()
    # 用下面这行代码替换掉原来复杂的if/else数据加载逻辑
    index = load_index()

    # 如果加载失败，显示错误并停止应用
    if index is None:
        st.error(
            "未能加载知识库。请先在终端运行 'python build_knowledge_base.py' 来构建知识库。"
        )
        st.stop()  # 停止执行


    # --- 对话引擎核心修改：使用 QueryFusionRetriever (官方推荐方案) ---
    if "chat_engine" not in st.session_state:
        with st.spinner("正在初始化高级对话引擎（首次启动较慢）..."):
            
            # --- 1. 初始化处理PDF的RAG查询引擎 ---
            # 修复：直接从docstore中获取节点，这是初始化BM25和动态调整k值所必需的
            nodes = list(index.docstore.docs.values())
            if not nodes:
                st.warning("知识库的文档库(docstore)为空。系统将仅能回答Excel相关问题。")
                # 创建一个空的RAG工具，以便路由器可以正常工作
                rag_query_engine = index.as_query_engine(similarity_top_k=0) 
            else:
                # 新增：动态调整top_k，防止其大于知识库节点总数
                num_nodes = len(nodes)
                if Config.TOP_K > num_nodes:
                    print(f"警告: 配置的 TOP_K ({Config.TOP_K}) 大于知识库中的节点数 ({num_nodes})。")
                    print(f"将自动调整检索的 k 值为 {num_nodes}。")
                    similarity_top_k = num_nodes
                else:
                    similarity_top_k = Config.TOP_K

                vector_retriever = index.as_retriever(similarity_top_k=similarity_top_k)
                bm25_retriever = BM25Retriever.from_defaults(
                    nodes=nodes,
                    similarity_top_k=similarity_top_k
                )

                # 集成TableRAG增强检索器
                if Config.USE_TABLERAG and TABLERAG_AVAILABLE:
                    try:
                        # 获取TableRAG配置
                        tablerag_config = get_tablerag_config(Config.TABLERAG_CONFIG_NAME)

                        # 创建TableRAG增强的融合检索器
                        retriever = create_tablerag_fusion_retriever(
                            vector_retriever=vector_retriever,
                            bm25_retriever=bm25_retriever,
                            tablerag_config=tablerag_config,
                            enable_table_processing=True,
                            similarity_top_k=similarity_top_k,
                            num_queries=4,
                            use_async=True,
                            verbose=True
                        )
                        print("TableRAG增强检索器初始化成功")
                    except Exception as e:
                        print(f"TableRAG增强检索器初始化失败: {e}")
                        # 回退到标准检索器
                        retriever = QueryFusionRetriever(
                            retrievers=[vector_retriever, bm25_retriever],
                            similarity_top_k=similarity_top_k,
                            num_queries=4,
                            use_async=True,
                            verbose=True
                        )
                else:
                    # 使用标准检索器
                    retriever = QueryFusionRetriever(
                        retrievers=[vector_retriever, bm25_retriever],
                        similarity_top_k=similarity_top_k,
                        num_queries=4,
                        use_async=True,
                        verbose=True
                    )
                relevance_filter = RelevanceScoreNodePostprocessor(threshold=Config.RELEVANCE_THRESHOLD)
                
                # 定义并保存我们最严格的提示词模板
                QA_PROMPT_TMPL_STR = (
                    "你是一个高度严谨的南水北调水利问答机器人，你的任务是只使用下面提供的上下文信息来回答问题。\n"
                    "**规则:**\n"
                    "1. 你的回答必须完全且仅基于上下文信息，严禁使用任何外部知识或进行推测。\n"
                    "2. 对于你的回答中的每一个观点、事实或数据，都必须在句末明确标注其来源，格式为 `[引用来源 n]`，其中n是上下文信息中对应条目的编号。\n"
                    "3. 如果多个来源支持同一个观点，可以标注多个来源，如 `[引用来源 1, 3]`。\n"
                    "4. 如果上下文中没有足够信息回答问题，必须直接回答：'根据提供的资料，我无法回答这个问题。' 不要尝试推测或使用你的背景知识。\n"
                    "5. 如果上下文中的信息与问题相关但不足以完整回答，请明确指出哪些方面的信息是可用的，哪些是缺失的。\n"
                    "6. 如果问题完全超出南水北调工程和水利领域，请回答：'这个问题超出了我的专业范围，我主要负责回答关于南水北调工程和水利相关的问题。'\n\n"
                    "**上下文信息:**\n"
                    "---------------------\n"
                    "{context_str}\n"
                    "---------------------\n"
                    "**思考过程:**\n"
                    "请在 <think> 标签中，首先逐一分析每个上下文片段与问题的相关性，然后评估上下文信息是否足够回答问题。如果信息不足，明确指出缺失的部分。最后制定一个严格遵循引用规则的回答计划。\n"
                    "**回答:**\n"
                    "问题: {query_str}\n"
                )
                qa_prompt_tmpl = PromptTemplate(QA_PROMPT_TMPL_STR)
                
                response_synthesizer = get_response_synthesizer(
                    response_mode="refine", text_qa_template=qa_prompt_tmpl
                )

                rag_query_engine = RetrieverQueryEngine.from_args(
                    retriever=retriever,
                    node_postprocessors=[reranker, relevance_filter],
                    response_synthesizer=response_synthesizer,
                )

            # --- 2. 初始化处理Excel的Pandas Agent ---
            excel_dfs = load_excel_dataframes(Config.DATA_DIR)
            pandas_tools = []
            if excel_dfs:
                # 新增：为Pandas Agent定义严格的指令
                PANDAS_AGENT_INSTRUCTION_STR = (
                    "你是一个Pandas数据分析助手。请根据用户问题生成简单的Python代码。\n"
                    "**重要规则:**\n"
                    "1. **只返回单行或简单的Python代码**，不要使用try-except块\n"
                    "2. **不要包含markdown标记**，不要添加解释文字\n"
                    "3. **优先使用这些安全的方法**: df.head(), df.info(), df.describe(), df.shape, df.columns.tolist()\n"
                    "4. **对于时间查询**，先显示时间列的范围: print(f'时间范围: {df[\"时间\"].min()} 到 {df[\"时间\"].max()}')\n"
                    "5. **如果查询复杂**，只显示数据概况: print(df.head())\n"
                    "6. **避免复杂的筛选和条件判断**\n\n"
                    "好的示例:\n"
                    "print(df.shape)\n"
                    "print(df.head())\n"
                    "print(df.columns.tolist())\n"
                    "print(df.describe())"
                )
                

                
                # 创建一个子类来覆盖PandasQueryEngine的方法
                from llama_index.experimental.query_engine.pandas import PandasQueryEngine as BasePandasQueryEngine
                
                class CustomPandasQueryEngine(BasePandasQueryEngine):
                    """自定义的PandasQueryEngine，增强错误处理能力"""
                    
                    def _query(self, query_str, **kwargs):
                        try:
                            return super()._query(query_str, **kwargs)
                        except Exception as e:
                            # 捕获所有异常，并返回一个友好的错误消息
                            print(f"查询处理时出错: {str(e)}")
                            return "print('查询处理时出错，请尝试重新表述您的问题。')"
                
                for name, df in excel_dfs.items():
                    # 使用自定义的PandasQueryEngine
                    pandas_query_engine = CustomPandasQueryEngine(
                        df=df, 
                        verbose=True,
                        instruction_str=PANDAS_AGENT_INSTRUCTION_STR,
                        output_processor=custom_output_processor
                    )
                    # 为该引擎创建一个工具，并附上详细描述
                    tool = QueryEngineTool.from_defaults(
                        query_engine=pandas_query_engine,
                        name=f"pandas_agent_{name}",
                        description=(
                            f"此工具用于回答关于 '{name}' 表格的问题。"
                            f"表格包含的列有: {', '.join(df.columns)}。"
                            "使用此工具可以进行数据查询、计算、聚合和分析。"
                        )
                    )
                    pandas_tools.append(tool)

            # --- 3. 将所有工具组合成一个列表 ---
            all_tools = [
                QueryEngineTool.from_defaults(
                    query_engine=rag_query_engine,
                    name="pdf_knowledge_base",
                    description="用于回答关于南水北调工程的政策、规定、技术规程等非结构化文档内容的问题。"
                )
            ] + pandas_tools

            # --- 4. 创建并保存智能路由器引擎 ---
            if len(all_tools) > 1:
                # 如果有多个工具（即PDF知识库 + 至少一个Excel Agent），则使用路由器
                st.session_state.chat_engine = RouterQueryEngine(
                    selector=LLMSingleSelector.from_defaults(),
                    query_engine_tools=all_tools,
                    verbose=True
                )
            elif all_tools:
                # 如果只有一个工具（通常是PDF知识库），则直接使用该引擎
                st.session_state.chat_engine = rag_query_engine
            else:
                st.error("没有任何可用的查询引擎（PDF或Excel），应用无法启动。")
                st.stop()


    # --- 聊天界面 ---
    # 显示历史消息
    for msg in st.session_state.messages:
        display_chat_message(msg)

    # 处理用户输入
    if prompt := st.chat_input("请输入您的问题..."):
        # 将用户消息添加到显示列表并立即显示
        user_message = {"role": "user", "content": prompt}
        st.session_state.messages.append(user_message)
        display_chat_message(user_message)

        # 处理查询并显示助手响应
        with st.spinner("正在使用融合检索模式检索并生成回答..."):
            # 直接使用我们强大的融合检索聊天引擎进行对话
            response = st.session_state.chat_engine.query(prompt)
            response_text = str(response)

            # 提取思考过程和参考文献
            think_contents = re.findall(r'<think>(.*?)</think>', response_text, re.DOTALL)
            cleaned_response = re.sub(r'<think>.*?</think>', '', response_text, flags=re.DOTALL).strip()
            
            # 新增：过滤掉LLM的内部注释和思考
            # 移除括号内的注释，如 "(Note: ...)" 或 "(注意：...)"
            cleaned_response = re.sub(r'\([Nn]ote:.*?\)', '', cleaned_response)
            cleaned_response = re.sub(r'\(注[：:].*?\)', '', cleaned_response)
            
            # 移除以"The provided context"或"根据提供的"开头的解释性文本
            cleaned_response = re.sub(r'^The provided context.*?(?=\n\n|\Z)', '', cleaned_response, flags=re.DOTALL|re.IGNORECASE)
            cleaned_response = re.sub(r'^根据提供的.*?(?=\n\n|\Z)', '', cleaned_response, flags=re.DOTALL)
            
            # 移除其他常见的模型自我解释文本
            common_explanations = [
                r'I need to answer based on.*?(?=\n\n|\Z)',
                r'我需要根据.*?(?=\n\n|\Z)',
                r'The answer is based on.*?(?=\n\n|\Z)',
                r'我的回答基于.*?(?=\n\n|\Z)',
                r'Thus, the original answer remains unchanged.*?(?=\n\n|\Z)'
            ]
            
            for pattern in common_explanations:
                cleaned_response = re.sub(pattern, '', cleaned_response, flags=re.DOTALL|re.IGNORECASE)
            
            # 清理多余的空行和空格
            cleaned_response = re.sub(r'\n{3,}', '\n\n', cleaned_response)
            cleaned_response = cleaned_response.strip()
            
            # 从响应对象中获取源节点
            source_nodes = response.source_nodes
            
            # 新增：在源节点中高亮相关句子
            try:
                reranker_model = reranker._model
                source_nodes = highlight_relevant_sentences(cleaned_response, source_nodes, reranker_model)
            except Exception as e:
                print(f"调用高亮函数时出错: {e}")
            
            # 检查是否无法回答问题
            if "我无法回答这个问题" in cleaned_response or "超出了我的专业范围" in cleaned_response:
                # 记录无法回答的问题
                log_unanswerable_question(prompt, source_nodes)

            assistant_message = {
                "role": "assistant",
                "content": response_text,
                "cleaned": cleaned_response,
                "think": think_contents,
                "reference_nodes": source_nodes
            }
            st.session_state.messages.append(assistant_message)

            # 保存当前问答对，用于文本导出
            st.session_state.last_qa = {
                "question": prompt,
                "answer": cleaned_response
            }

            # 使用rerun来刷新界面并显示助手的完整消息
            st.rerun()

    # 显示下载按钮（对于最近的一次对话）
    if "last_qa" in st.session_state:
        col1, col2 = st.columns([3, 1])
        with col2:
            # 直接准备文本内容，不需要额外的按钮点击
            q = st.session_state.last_qa["question"]
            a = st.session_state.last_qa["answer"]
            content = generate_single_qa_text(q, a).decode('utf-8')
            timestamp = time.strftime("%Y%m%d-%H%M%S")

            # 使用与侧边栏相同的下载按钮实现
            st.download_button(
                label="📄 导出此问答",
                data=content,
                file_name=f"南水北调问答-{timestamp}.txt",
                mime="text/plain",
                key=f"download_single_{len(st.session_state.messages)}_{timestamp}"
            )

if __name__ == "__main__":
    main()