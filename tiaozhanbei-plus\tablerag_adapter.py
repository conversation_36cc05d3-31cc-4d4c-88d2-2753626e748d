# tablerag_adapter.py
"""
TableRAG适配器模块
将TableRAG的表格处理功能封装成与现有RAG系统兼容的接口
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import List, Dict, Optional, Any
import warnings
warnings.filterwarnings("ignore")

# 添加TableRAG路径到系统路径
TABLERAG_PATH = Path(__file__).parent / "tablerag-main"
if str(TABLERAG_PATH) not in sys.path:
    sys.path.insert(0, str(TABLERAG_PATH))

try:
    from src.table_master.table_main import TableClarifier
    from src.table_loader.data_loader.table_parser.table_parsing import TableParser
    from src.table_loader.data_loader.table_parser.table_linearizer import StructuredDataLinearizer
    from src.table_loader.data_loader.table_parser.type_sets import (
        TableSerializationType,
        TableFilterType,
        TableClarificationType
    )
    from src.llm.llm_generator.llm_generating import LLM_Generator
    from src.colbert_pipeline.colbert_main import ColBERTRetriever
except ImportError as e:
    print(f"警告: 无法导入TableRAG模块: {e}")
    print("请确保TableRAG依赖已正确安装")


class TableRAGConfig:
    """TableRAG配置类"""

    def __init__(self):
        self.embedding_type = "text-embedding-3-large"
        self.table_filter_name = "llm_based_filter"  # 或 "semetics_based_filter"
        self.table_clarifier_name = "term_explanations_and_table_summary"  # 或 "None"
        self.table_format = "markdown"  # 或 "html", "string"
        self.top_k = 5
        self.use_table_filter = True
        self.use_self_consistency = False
        self.processing_mode = "tablerag"  # "simple", "tablerag", "hybrid"


class ExcelToTableRAGConverter:
    """Excel文件到TableRAG格式的转换器"""
    
    def __init__(self, config: TableRAGConfig):
        self.config = config
        self.linearizer = StructuredDataLinearizer()
        
    def excel_to_tablerag_format(self, excel_file: Path, sheet_name: str, df: pd.DataFrame) -> Dict:
        """
        将Excel DataFrame转换为TableRAG期望的格式
        
        Args:
            excel_file: Excel文件路径
            sheet_name: 工作表名称
            df: pandas DataFrame
            
        Returns:
            TableRAG格式的字典
        """
        # 确保DataFrame不为空
        if df.empty:
            return None
            
        # 清理数据
        df = df.fillna("")
        
        # 转换为TableRAG格式
        tablerag_format = {
            "title": f"{excel_file.stem}_{sheet_name}",
            "context": [f"来源文件: {excel_file.name}", f"工作表: {sheet_name}"],
            "table": {
                "header": df.columns.tolist(),
                "rows": df.values.tolist(),
                "caption": f"表格来源: {excel_file.name} - {sheet_name}"
            },
            "metadata": {
                "source_file": str(excel_file),
                "sheet_name": sheet_name,
                "content_type": "excel_table"
            }
        }
        
        return tablerag_format


class TableRAGAdapter:
    """TableRAG适配器主类"""

    def __init__(self, config: TableRAGConfig = None):
        self.config = config or TableRAGConfig()
        self.converter = ExcelToTableRAGConverter(self.config)
        self.llm_generator = None
        self.table_clarifier = None

        # 检查是否为低内存模式
        self.low_memory_mode = getattr(self.config, 'max_table_size', 1000) < 500

        # 初始化TableRAG组件
        self._init_tablerag_components()
        
    def _init_tablerag_components(self):
        """初始化TableRAG组件"""
        try:
            # 初始化LLM生成器
            self.llm_generator = LLM_Generator()
            
            # 注意：TableClarifier需要特定的任务名称和数据集
            # 这里我们创建一个通用的处理器
            print("TableRAG组件初始化成功")
            
        except Exception as e:
            print(f"TableRAG组件初始化失败: {e}")
            self.llm_generator = None
    
    def process_excel_files(self, data_dir: str) -> List[Dict]:
        """
        使用TableRAG处理Excel文件
        
        Args:
            data_dir: 数据目录路径
            
        Returns:
            处理后的数据列表
        """
        excel_files = list(Path(data_dir).rglob("*.xlsx"))
        if not excel_files:
            print("未发现Excel文件，跳过TableRAG处理。")
            return []
            
        all_processed_data = []
        
        for excel_file in excel_files:
            try:
                # 读取Excel文件的所有工作表
                sheets = pd.read_excel(excel_file, sheet_name=None, engine='openpyxl')
                
                for sheet_name, df in sheets.items():
                    if df.empty:
                        continue
                        
                    # 转换为TableRAG格式
                    tablerag_data = self.converter.excel_to_tablerag_format(
                        excel_file, sheet_name, df
                    )
                    
                    if tablerag_data is None:
                        continue
                    
                    # 应用TableRAG处理
                    processed_data = self._apply_tablerag_processing(tablerag_data)
                    
                    if processed_data:
                        all_processed_data.extend(processed_data)
                        
            except Exception as e:
                print(f"处理Excel文件 {excel_file.name} 时出错: {e}")
                continue
                
        print(f"TableRAG处理完成，共生成 {len(all_processed_data)} 个数据条目")
        return all_processed_data
    
    def _apply_tablerag_processing(self, tablerag_data: Dict) -> List[Dict]:
        """
        应用TableRAG的表格处理逻辑
        
        Args:
            tablerag_data: TableRAG格式的表格数据
            
        Returns:
            处理后的数据列表
        """
        processed_entries = []
        
        try:
            # 1. 表格线性化 - 转换为指定格式
            if self.config.table_format == "markdown":
                linearized_table = self.converter.linearizer.retrieve_linear_function(
                    TableSerializationType.markdown, tablerag_data
                )
            elif self.config.table_format == "html":
                linearized_table = self.converter.linearizer.retrieve_linear_function(
                    TableSerializationType.html, tablerag_data
                )
            else:
                # 默认使用字符串格式
                linearized_table = self._table_to_string(tablerag_data)
            
            # 2. 生成表格摘要和术语解释（如果启用）
            enhanced_info = self._generate_table_enhancements(tablerag_data, linearized_table)
            
            # 3. 创建增强的表格表示
            enhanced_content = self._create_enhanced_content(
                linearized_table, enhanced_info, tablerag_data
            )
            
            # 4. 如果启用了表格过滤，可以进一步处理
            # 这里暂时跳过，因为过滤需要查询上下文
            
            processed_entries.append({
                "content": enhanced_content,
                "metadata": {
                    **tablerag_data["metadata"],
                    "processing_method": "tablerag",
                    "table_format": self.config.table_format,
                    "enhanced": bool(enhanced_info)
                },
                "raw_table": linearized_table,
                "enhancements": enhanced_info
            })
            
        except Exception as e:
            print(f"TableRAG处理失败: {e}")
            # 回退到简单处理
            fallback_content = self._simple_table_processing(tablerag_data)
            processed_entries.append({
                "content": fallback_content,
                "metadata": {
                    **tablerag_data["metadata"],
                    "processing_method": "fallback"
                }
            })
            
        return processed_entries
    
    def _table_to_string(self, tablerag_data: Dict) -> str:
        """将表格转换为字符串格式"""
        table = tablerag_data["table"]
        header = table["header"]
        rows = table["rows"]
        
        # 创建简单的表格字符串表示
        lines = []
        lines.append(" | ".join(header))
        lines.append("-" * len(lines[0]))
        
        for row in rows:
            lines.append(" | ".join(str(cell) for cell in row))
            
        return "\n".join(lines)
    
    def _generate_table_enhancements(self, tablerag_data: Dict, linearized_table: str) -> Dict:
        """生成表格增强信息（摘要和术语解释）"""
        enhancements = {}
        
        if self.config.table_clarifier_name == "None":
            return enhancements
            
        try:
            if self.llm_generator is None:
                return enhancements
                
            # 生成表格摘要
            if "table_summary" in self.config.table_clarifier_name:
                summary_prompt = f"""
                请为以下表格生成一个简洁的摘要，说明表格的主要内容和结构：
                
                表格标题: {tablerag_data.get('title', '未知')}
                表格内容:
                {linearized_table[:1000]}  # 限制长度
                
                请用中文回答，控制在100字以内。
                """
                
                summary = self.llm_generator.call_llm(summary_prompt)
                enhancements["table_summary"] = summary
            
            # 生成术语解释
            if "term_explanations" in self.config.table_clarifier_name:
                terms_prompt = f"""
                请识别以下表格中可能需要解释的专业术语或缩写，并提供简短解释：
                
                表格标题: {tablerag_data.get('title', '未知')}
                表格列名: {', '.join(tablerag_data['table']['header'])}
                
                请用中文回答，每个术语的解释控制在30字以内。
                """
                
                terms = self.llm_generator.call_llm(terms_prompt)
                enhancements["terms_explanation"] = terms
                
        except Exception as e:
            print(f"生成表格增强信息时出错: {e}")
            
        return enhancements
    
    def _create_enhanced_content(self, linearized_table: str, enhanced_info: Dict, tablerag_data: Dict) -> str:
        """创建增强的表格内容"""
        content_parts = []
        
        # 添加表格标题和上下文
        content_parts.append(f"表格: {tablerag_data.get('title', '未知表格')}")
        
        if tablerag_data.get('context'):
            content_parts.append(f"上下文: {'; '.join(tablerag_data['context'])}")
        
        # 添加表格摘要
        if enhanced_info.get("table_summary"):
            content_parts.append(f"表格摘要: {enhanced_info['table_summary']}")
        
        # 添加术语解释
        if enhanced_info.get("terms_explanation"):
            content_parts.append(f"术语解释: {enhanced_info['terms_explanation']}")
        
        # 添加表格内容
        content_parts.append("表格内容:")
        content_parts.append(linearized_table)
        
        return "\n\n".join(content_parts)
    
    def _simple_table_processing(self, tablerag_data: Dict) -> str:
        """简单的表格处理（回退方案）"""
        table = tablerag_data["table"]
        header = table["header"]
        rows = table["rows"]
        
        content = f"表格: {tablerag_data.get('title', '未知表格')}\n"
        content += f"列名: {', '.join(header)}\n"
        content += f"数据行数: {len(rows)}\n"
        
        # 添加前几行数据作为示例
        for i, row in enumerate(rows[:3]):  # 只显示前3行
            row_data = {header[j]: str(row[j]) if j < len(row) else "" for j in range(len(header))}
            content += f"行 {i+1}: {row_data}\n"
            
        return content


# 便捷函数
def create_tablerag_adapter(config: Dict = None) -> TableRAGAdapter:
    """
    创建TableRAG适配器实例
    
    Args:
        config: 配置字典
        
    Returns:
        TableRAGAdapter实例
    """
    tablerag_config = TableRAGConfig()
    
    if config:
        for key, value in config.items():
            if hasattr(tablerag_config, key):
                setattr(tablerag_config, key, value)
    
    return TableRAGAdapter(tablerag_config)


def process_excel_with_tablerag(data_dir: str, config: Dict = None) -> List[Dict]:
    """
    使用TableRAG处理Excel文件的便捷函数
    
    Args:
        data_dir: 数据目录
        config: 配置字典
        
    Returns:
        处理后的数据列表
    """
    adapter = create_tablerag_adapter(config)
    return adapter.process_excel_files(data_dir)
