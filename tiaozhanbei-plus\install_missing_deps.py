# install_missing_deps.py
"""
安装TableRAG集成所需的缺失依赖
"""

import subprocess
import sys


def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True, check=True)
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def main():
    """安装缺失的依赖"""
    print("安装TableRAG集成所需的缺失依赖...")
    
    # 核心缺失依赖
    missing_deps = [
        "langchain-community",  # 修复 langchain_community 导入错误
        "datasets>=2.11.0",    # TableRAG需要
        "beautifulsoup4>=4.12.0",  # 文本处理
        "lxml>=4.9.0",         # XML处理
    ]
    
    success_count = 0
    total_count = len(missing_deps)
    
    for dep in missing_deps:
        if install_package(dep):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("✓ 所有依赖安装成功！")
        print("\n下一步:")
        print("1. 运行: python test_tablerag_integration.py")
        print("2. 如果还有问题，运行: python validate_tablerag_setup.py")
    else:
        print("⚠ 部分依赖安装失败，请手动安装失败的包")
        print("\n手动安装命令:")
        for dep in missing_deps:
            print(f"pip install {dep}")


if __name__ == "__main__":
    main()
