# tablerag_query_processor.py
"""
TableRAG查询处理器
在查询时动态应用表格过滤和澄清功能
"""

import sys
from pathlib import Path
from typing import List, Dict, Optional, Any
import warnings
warnings.filterwarnings("ignore")

# 添加TableRAG路径
TABLERAG_PATH = Path(__file__).parent / "tablerag-main"
if str(TABLERAG_PATH) not in sys.path:
    sys.path.insert(0, str(TABLERAG_PATH))

try:
    from src.table_master.table_filter import TableFilter
    from src.table_master.table_clarifier import TableClarification
    from src.llm.llm_generator.llm_generating import LLM_Generator
    from src.table_loader.data_loader.table_parser.table_linearizer import StructuredDataLinearizer
    from src.table_loader.data_loader.table_parser.type_sets import (
        TableSerializationType, 
        TableFilterType, 
        TableClarificationType
    )
    TABLERAG_COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"TableRAG组件导入失败: {e}")
    TABLERAG_COMPONENTS_AVAILABLE = False

from tablerag_config import TableRAGIntegrationConfig


class TableRAGQueryProcessor:
    """TableRAG查询处理器"""
    
    def __init__(self, config: TableRAGIntegrationConfig):
        self.config = config
        self.llm_generator = None
        self.table_filter = None
        self.table_clarifier = None
        self.linearizer = StructuredDataLinearizer() if TABLERAG_COMPONENTS_AVAILABLE else None
        
        if TABLERAG_COMPONENTS_AVAILABLE and config.enabled:
            self._init_components()
    
    def _init_components(self):
        """初始化TableRAG组件"""
        try:
            # 初始化LLM生成器
            self.llm_generator = LLM_Generator()
            
            # 初始化表格过滤器
            if self.config.use_table_filter and self.config.table_filter_name != "None":
                self.table_filter = TableFilter(
                    call_llm=self.llm_generator,
                    task_name="custom",  # 自定义任务
                    split="custom",
                    table_filter_name=self.config.table_filter_name,
                    embedding_type=self.config.embedding_type,
                    top_k=self.config.top_k,
                    whether_column_grounding=True
                )
            
            # 初始化表格澄清器
            if self.config.table_clarifier_name != "None":
                self.table_clarifier = TableClarification(
                    call_llm=self.llm_generator,
                    task_name="custom",
                    split="custom",
                    table_clarifier_name=self.config.table_clarifier_name
                )
            
            print("TableRAG查询处理器初始化成功")
            
        except Exception as e:
            print(f"TableRAG查询处理器初始化失败: {e}")
            self.llm_generator = None
            self.table_filter = None
            self.table_clarifier = None
    
    def process_query_with_table_context(self, query: str, table_nodes: List[Any]) -> List[Dict]:
        """
        使用TableRAG处理查询和表格上下文
        
        Args:
            query: 用户查询
            table_nodes: 表格节点列表
            
        Returns:
            处理后的表格上下文列表
        """
        if not self.config.enabled or not TABLERAG_COMPONENTS_AVAILABLE:
            return self._fallback_processing(table_nodes)
        
        processed_contexts = []
        
        for node in table_nodes:
            try:
                # 检查是否是TableRAG增强的节点
                if node.metadata.get("node_type") == "tablerag_enhanced":
                    # 已经是增强节点，直接使用
                    processed_contexts.append({
                        "content": node.text,
                        "metadata": node.metadata,
                        "node_id": node.id_,
                        "processing_type": "pre_enhanced"
                    })
                else:
                    # 尝试动态处理传统节点
                    processed_context = self._process_traditional_node(query, node)
                    if processed_context:
                        processed_contexts.append(processed_context)
                        
            except Exception as e:
                print(f"处理节点 {node.id_} 时出错: {e}")
                # 回退到原始内容
                processed_contexts.append({
                    "content": node.text,
                    "metadata": node.metadata,
                    "node_id": node.id_,
                    "processing_type": "fallback"
                })
        
        return processed_contexts
    
    def _process_traditional_node(self, query: str, node: Any) -> Optional[Dict]:
        """处理传统的表格节点"""
        try:
            # 尝试从节点内容中提取表格信息
            table_data = self._extract_table_from_node(node)
            if not table_data:
                return None
            
            # 应用表格过滤
            if self.table_filter and self.config.use_table_filter:
                try:
                    filtered_table = self.table_filter.run(query, table_data)
                    # 更新表格数据
                    table_data["table"]["rows"] = filtered_table.values.tolist()
                    table_data["table"]["header"] = filtered_table.columns.tolist()
                except Exception as e:
                    print(f"表格过滤失败: {e}")
            
            # 应用表格澄清
            enhanced_info = {}
            if self.table_clarifier:
                try:
                    enhanced_info = self.table_clarifier.run(table_data)
                except Exception as e:
                    print(f"表格澄清失败: {e}")
            
            # 生成增强内容
            enhanced_content = self._create_enhanced_content_from_table(
                table_data, enhanced_info, query
            )
            
            return {
                "content": enhanced_content,
                "metadata": {
                    **node.metadata,
                    "processing_type": "dynamic_enhanced",
                    "query_processed": True,
                    "has_filtering": bool(self.table_filter and self.config.use_table_filter),
                    "has_clarification": bool(enhanced_info)
                },
                "node_id": node.id_,
                "processing_type": "dynamic_enhanced"
            }
            
        except Exception as e:
            print(f"动态处理节点失败: {e}")
            return None
    
    def _extract_table_from_node(self, node: Any) -> Optional[Dict]:
        """从节点中提取表格数据"""
        try:
            # 这里需要根据你的节点格式来解析表格
            # 由于原始节点是文本格式，这里做简单的解析
            content = node.text
            metadata = node.metadata
            
            # 如果有原始表格预览，尝试使用它
            if "raw_table_preview" in metadata:
                # 简单的表格解析逻辑
                lines = metadata["raw_table_preview"].split('\n')
                if len(lines) >= 2:
                    header = lines[0].split(' | ')
                    rows = []
                    for line in lines[2:]:  # 跳过分隔线
                        if line.strip():
                            rows.append(line.split(' | '))
                    
                    return {
                        "title": f"{metadata.get('source_file', 'Unknown')}_{metadata.get('sheet_name', 'Unknown')}",
                        "context": [f"来源: {metadata.get('source_file', 'Unknown')}"],
                        "table": {
                            "header": header,
                            "rows": rows,
                            "caption": f"表格: {metadata.get('sheet_name', 'Unknown')}"
                        }
                    }
            
            # 如果没有原始表格，尝试从内容中解析
            # 这里可以添加更复杂的解析逻辑
            return None
            
        except Exception as e:
            print(f"提取表格数据失败: {e}")
            return None
    
    def _create_enhanced_content_from_table(self, table_data: Dict, enhanced_info: Dict, query: str) -> str:
        """从表格数据创建增强内容"""
        content_parts = []
        
        # 添加查询上下文
        content_parts.append(f"针对查询 '{query}' 的表格分析:")
        
        # 添加表格标题
        content_parts.append(f"表格: {table_data.get('title', '未知表格')}")
        
        # 添加表格摘要
        if enhanced_info.get("table_summary"):
            content_parts.append(f"表格摘要: {enhanced_info['table_summary']}")
        
        # 添加术语解释
        if enhanced_info.get("terms_explanation"):
            content_parts.append(f"术语解释: {enhanced_info['terms_explanation']}")
        
        # 添加表格内容
        if self.linearizer:
            try:
                if self.config.table_format == "markdown":
                    linearized_table = self.linearizer.retrieve_linear_function(
                        TableSerializationType.markdown, table_data
                    )
                elif self.config.table_format == "html":
                    linearized_table = self.linearizer.retrieve_linear_function(
                        TableSerializationType.html, table_data
                    )
                else:
                    linearized_table = self._table_to_string(table_data)
                
                content_parts.append("表格内容:")
                content_parts.append(linearized_table)
                
            except Exception as e:
                print(f"表格线性化失败: {e}")
                content_parts.append("表格内容: [处理失败]")
        
        return "\n\n".join(content_parts)
    
    def _table_to_string(self, table_data: Dict) -> str:
        """将表格转换为字符串格式"""
        table = table_data["table"]
        header = table["header"]
        rows = table["rows"]
        
        lines = []
        lines.append(" | ".join(header))
        lines.append("-" * len(lines[0]))
        
        for row in rows:
            lines.append(" | ".join(str(cell) for cell in row))
            
        return "\n".join(lines)
    
    def _fallback_processing(self, table_nodes: List[Any]) -> List[Dict]:
        """回退处理方法"""
        return [
            {
                "content": node.text,
                "metadata": node.metadata,
                "node_id": node.id_,
                "processing_type": "fallback"
            }
            for node in table_nodes
        ]


def create_tablerag_query_processor(config: TableRAGIntegrationConfig = None) -> TableRAGQueryProcessor:
    """创建TableRAG查询处理器"""
    if config is None:
        from tablerag_config import get_tablerag_config
        config = get_tablerag_config("enhanced")
    
    return TableRAGQueryProcessor(config)
