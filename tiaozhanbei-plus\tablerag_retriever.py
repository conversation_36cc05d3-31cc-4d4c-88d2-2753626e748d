# tablerag_retriever.py
"""
TableRAG增强检索器
集成TableRAG的表格处理能力到LlamaIndex检索流程中
"""

from typing import List, Optional, Any, Dict
from llama_index.core.retrievers import BaseRetriever
from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.callbacks import CallbackManager
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.retrievers.bm25 import BM25Retriever
from llama_index.core.retrievers import QueryFusionRetriever

from tablerag_query_processor import TableRAGQueryProcessor, create_tablerag_query_processor
from tablerag_config import TableRAGIntegrationConfig


class TableRAGEnhancedRetriever(BaseRetriever):
    """TableRAG增强检索器"""
    
    def __init__(
        self,
        base_retriever: BaseRetriever,
        tablerag_config: Optional[TableRAGIntegrationConfig] = None,
        enable_table_processing: bool = True,
        callback_manager: Optional[CallbackManager] = None,
    ):
        """
        初始化TableRAG增强检索器
        
        Args:
            base_retriever: 基础检索器
            tablerag_config: TableRAG配置
            enable_table_processing: 是否启用表格处理
            callback_manager: 回调管理器
        """
        super().__init__(callback_manager=callback_manager)
        self.base_retriever = base_retriever
        self.enable_table_processing = enable_table_processing
        
        # 初始化TableRAG查询处理器
        if enable_table_processing:
            self.query_processor = create_tablerag_query_processor(tablerag_config)
        else:
            self.query_processor = None
    
    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """
        执行检索
        
        Args:
            query_bundle: 查询包
            
        Returns:
            检索到的节点列表
        """
        # 首先使用基础检索器获取节点
        base_nodes = self.base_retriever.retrieve(query_bundle)
        
        if not self.enable_table_processing or not self.query_processor:
            return base_nodes
        
        # 分离表格节点和非表格节点
        table_nodes = []
        non_table_nodes = []
        
        for node_with_score in base_nodes:
            node = node_with_score.node
            metadata = node.metadata
            
            # 检查是否是表格相关的节点
            if (metadata.get("content_type") in ["excel_row", "excel_table"] or 
                metadata.get("node_type") == "tablerag_enhanced" or
                metadata.get("processing_method") == "tablerag"):
                table_nodes.append(node_with_score)
            else:
                non_table_nodes.append(node_with_score)
        
        # 处理表格节点
        if table_nodes:
            try:
                enhanced_table_nodes = self._process_table_nodes(
                    query_bundle.query_str, table_nodes
                )
                # 合并处理后的表格节点和非表格节点
                return non_table_nodes + enhanced_table_nodes
            except Exception as e:
                print(f"表格节点处理失败: {e}")
                # 如果处理失败，返回原始节点
                return base_nodes
        
        return base_nodes
    
    def _process_table_nodes(self, query: str, table_nodes: List[NodeWithScore]) -> List[NodeWithScore]:
        """
        处理表格节点
        
        Args:
            query: 查询字符串
            table_nodes: 表格节点列表
            
        Returns:
            处理后的表格节点列表
        """
        processed_nodes = []
        
        # 提取节点对象
        nodes = [node_with_score.node for node_with_score in table_nodes]
        
        # 使用TableRAG查询处理器处理
        processed_contexts = self.query_processor.process_query_with_table_context(query, nodes)
        
        # 重新创建NodeWithScore对象
        for i, (original_node_with_score, processed_context) in enumerate(zip(table_nodes, processed_contexts)):
            try:
                # 创建新的节点
                from llama_index.core.schema import TextNode
                
                enhanced_node = TextNode(
                    text=processed_context["content"],
                    id_=processed_context["node_id"],
                    metadata=processed_context["metadata"]
                )
                
                # 保持原始的分数
                enhanced_node_with_score = NodeWithScore(
                    node=enhanced_node,
                    score=original_node_with_score.score
                )
                
                processed_nodes.append(enhanced_node_with_score)
                
            except Exception as e:
                print(f"创建增强节点失败: {e}")
                # 如果失败，使用原始节点
                processed_nodes.append(original_node_with_score)
        
        return processed_nodes


class TableRAGQueryFusionRetriever(QueryFusionRetriever):
    """TableRAG增强的查询融合检索器"""
    
    def __init__(
        self,
        retrievers: List[BaseRetriever],
        tablerag_config: Optional[TableRAGIntegrationConfig] = None,
        enable_table_processing: bool = True,
        **kwargs
    ):
        """
        初始化TableRAG查询融合检索器
        
        Args:
            retrievers: 检索器列表
            tablerag_config: TableRAG配置
            enable_table_processing: 是否启用表格处理
            **kwargs: 其他参数
        """
        # 将每个检索器包装为TableRAG增强检索器
        if enable_table_processing:
            enhanced_retrievers = []
            for retriever in retrievers:
                if isinstance(retriever, TableRAGEnhancedRetriever):
                    enhanced_retrievers.append(retriever)
                else:
                    enhanced_retrievers.append(
                        TableRAGEnhancedRetriever(
                            base_retriever=retriever,
                            tablerag_config=tablerag_config,
                            enable_table_processing=enable_table_processing
                        )
                    )
            super().__init__(retrievers=enhanced_retrievers, **kwargs)
        else:
            super().__init__(retrievers=retrievers, **kwargs)


def create_tablerag_enhanced_retriever(
    base_retriever: BaseRetriever,
    tablerag_config: Optional[TableRAGIntegrationConfig] = None,
    enable_table_processing: bool = True
) -> TableRAGEnhancedRetriever:
    """
    创建TableRAG增强检索器的便捷函数
    
    Args:
        base_retriever: 基础检索器
        tablerag_config: TableRAG配置
        enable_table_processing: 是否启用表格处理
        
    Returns:
        TableRAG增强检索器
    """
    return TableRAGEnhancedRetriever(
        base_retriever=base_retriever,
        tablerag_config=tablerag_config,
        enable_table_processing=enable_table_processing
    )


def create_tablerag_fusion_retriever(
    vector_retriever: VectorIndexRetriever,
    bm25_retriever: BM25Retriever,
    tablerag_config: Optional[TableRAGIntegrationConfig] = None,
    enable_table_processing: bool = True,
    **fusion_kwargs
) -> TableRAGQueryFusionRetriever:
    """
    创建TableRAG查询融合检索器的便捷函数
    
    Args:
        vector_retriever: 向量检索器
        bm25_retriever: BM25检索器
        tablerag_config: TableRAG配置
        enable_table_processing: 是否启用表格处理
        **fusion_kwargs: 融合检索器的其他参数
        
    Returns:
        TableRAG查询融合检索器
    """
    return TableRAGQueryFusionRetriever(
        retrievers=[vector_retriever, bm25_retriever],
        tablerag_config=tablerag_config,
        enable_table_processing=enable_table_processing,
        **fusion_kwargs
    )


# 示例用法
if __name__ == "__main__":
    # 这里可以添加测试代码
    from tablerag_config import get_tablerag_config
    
    config = get_tablerag_config("enhanced")
    print(f"TableRAG配置: {config.table_format}, 过滤器: {config.table_filter_name}")
    
    # 创建查询处理器
    processor = create_tablerag_query_processor(config)
    print("TableRAG查询处理器创建成功")
