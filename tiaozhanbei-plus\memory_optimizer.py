# memory_optimizer.py
"""
6GB显存优化工具
监控和优化内存使用，确保TableRAG在低显存环境下正常运行
"""

import gc
import torch
import psutil
import os
from typing import Dict, Any, Optional
import warnings
warnings.filterwarnings("ignore")


class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self, max_gpu_memory_gb: float = 5.5):  # 为6GB显存预留0.5GB
        self.max_gpu_memory_gb = max_gpu_memory_gb
        self.max_gpu_memory_bytes = max_gpu_memory_gb * 1024 * 1024 * 1024
        self.gpu_available = torch.cuda.is_available()
        
    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存信息"""
        info = {
            "cpu_memory": {
                "total": psutil.virtual_memory().total / (1024**3),
                "available": psutil.virtual_memory().available / (1024**3),
                "percent": psutil.virtual_memory().percent
            }
        }
        
        if self.gpu_available:
            try:
                gpu_memory = torch.cuda.get_device_properties(0).total_memory
                gpu_allocated = torch.cuda.memory_allocated(0)
                gpu_cached = torch.cuda.memory_reserved(0)
                
                info["gpu_memory"] = {
                    "total": gpu_memory / (1024**3),
                    "allocated": gpu_allocated / (1024**3),
                    "cached": gpu_cached / (1024**3),
                    "free": (gpu_memory - gpu_cached) / (1024**3)
                }
            except Exception as e:
                info["gpu_memory"] = {"error": str(e)}
        
        return info
    
    def check_memory_usage(self) -> bool:
        """检查内存使用是否在安全范围内"""
        info = self.get_memory_info()
        
        # 检查CPU内存
        if info["cpu_memory"]["percent"] > 85:
            print(f"⚠️ CPU内存使用率过高: {info['cpu_memory']['percent']:.1f}%")
            return False
        
        # 检查GPU内存
        if self.gpu_available and "gpu_memory" in info and "allocated" in info["gpu_memory"]:
            gpu_usage = info["gpu_memory"]["allocated"]
            if gpu_usage > self.max_gpu_memory_gb:
                print(f"⚠️ GPU内存使用过高: {gpu_usage:.2f}GB / {self.max_gpu_memory_gb}GB")
                return False
        
        return True
    
    def cleanup_memory(self):
        """清理内存"""
        # 清理Python垃圾回收
        gc.collect()
        
        # 清理GPU缓存
        if self.gpu_available:
            try:
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            except Exception as e:
                print(f"GPU缓存清理失败: {e}")
    
    def optimize_for_low_memory(self) -> Dict[str, Any]:
        """为低内存环境优化配置"""
        config = {
            # TableRAG配置优化
            "tablerag": {
                "enabled": True,
                "table_filter_name": "None",  # 关闭过滤器
                "table_clarifier_name": "None",  # 关闭澄清器
                "table_format": "string",  # 使用最简单格式
                "use_table_filter": False,
                "use_self_consistency": False,
                "max_table_size": 200,  # 严格限制表格大小
                "max_content_length": 1000,  # 严格限制内容长度
                "parallel_processing": False,
                "max_workers": 1,
                "enable_cache": True,
                "batch_size": 1  # 最小批处理大小
            },
            
            # 模型配置优化
            "model": {
                "use_cpu_only": not self.gpu_available,  # 如果GPU内存不足，使用CPU
                "max_seq_length": 512,  # 限制序列长度
                "gradient_checkpointing": True,  # 启用梯度检查点
                "mixed_precision": True,  # 使用混合精度
                "model_offload": True  # 启用模型卸载
            },
            
            # 处理配置优化
            "processing": {
                "chunk_size": 256,  # 减小块大小
                "chunk_overlap": 25,  # 减小重叠
                "max_chunks_per_doc": 50,  # 限制每个文档的块数
                "enable_streaming": True  # 启用流式处理
            }
        }
        
        return config
    
    def monitor_memory_during_processing(self, func, *args, **kwargs):
        """在处理过程中监控内存"""
        print("🔍 开始内存监控...")
        
        # 处理前清理内存
        self.cleanup_memory()
        initial_info = self.get_memory_info()
        print(f"初始内存状态: CPU {initial_info['cpu_memory']['percent']:.1f}%", end="")
        
        if "gpu_memory" in initial_info:
            print(f", GPU {initial_info['gpu_memory']['allocated']:.2f}GB")
        else:
            print()
        
        try:
            # 执行函数
            result = func(*args, **kwargs)
            
            # 处理后检查内存
            final_info = self.get_memory_info()
            print(f"处理后内存状态: CPU {final_info['cpu_memory']['percent']:.1f}%", end="")
            
            if "gpu_memory" in final_info:
                print(f", GPU {final_info['gpu_memory']['allocated']:.2f}GB")
            else:
                print()
            
            # 如果内存使用过高，进行清理
            if not self.check_memory_usage():
                print("🧹 执行内存清理...")
                self.cleanup_memory()
            
            return result
            
        except Exception as e:
            print(f"❌ 处理过程中出现错误: {e}")
            print("🧹 执行紧急内存清理...")
            self.cleanup_memory()
            raise


def create_low_memory_config() -> Dict[str, Any]:
    """创建低内存配置"""
    optimizer = MemoryOptimizer()
    return optimizer.optimize_for_low_memory()


def check_system_compatibility() -> bool:
    """检查系统兼容性"""
    print("🔍 检查系统兼容性...")
    
    optimizer = MemoryOptimizer()
    info = optimizer.get_memory_info()
    
    # 检查CPU内存
    cpu_memory_gb = info["cpu_memory"]["total"]
    if cpu_memory_gb < 8:
        print(f"⚠️ CPU内存较少: {cpu_memory_gb:.1f}GB，建议至少8GB")
        return False
    
    # 检查GPU内存
    if optimizer.gpu_available and "gpu_memory" in info:
        gpu_memory_gb = info["gpu_memory"]["total"]
        print(f"✓ 检测到GPU: {gpu_memory_gb:.1f}GB显存")
        
        if gpu_memory_gb < 4:
            print("⚠️ GPU显存不足4GB，建议使用CPU模式")
            return False
        elif gpu_memory_gb < 8:
            print("✓ GPU显存适中，将使用低内存优化模式")
    else:
        print("ℹ️ 未检测到GPU，将使用CPU模式")
    
    print("✓ 系统兼容性检查通过")
    return True


def apply_memory_optimizations():
    """应用内存优化"""
    print("🚀 应用内存优化设置...")
    
    # 设置环境变量
    os.environ["TOKENIZERS_PARALLELISM"] = "false"  # 关闭tokenizer并行
    os.environ["OMP_NUM_THREADS"] = "1"  # 限制OpenMP线程数
    
    # PyTorch优化
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = False  # 关闭cudnn benchmark
        torch.backends.cudnn.deterministic = True  # 启用确定性模式
        
        # 设置GPU内存分配策略
        try:
            torch.cuda.set_per_process_memory_fraction(0.9)  # 限制GPU内存使用
        except Exception as e:
            print(f"设置GPU内存限制失败: {e}")
    
    print("✓ 内存优化设置已应用")


# 全局内存优化器实例
memory_optimizer = MemoryOptimizer()


def memory_safe_wrapper(func):
    """内存安全装饰器"""
    def wrapper(*args, **kwargs):
        return memory_optimizer.monitor_memory_during_processing(func, *args, **kwargs)
    return wrapper


if __name__ == "__main__":
    # 测试内存优化器
    print("TableRAG内存优化器测试")
    print("=" * 40)
    
    # 检查系统兼容性
    compatible = check_system_compatibility()
    
    if compatible:
        # 应用优化
        apply_memory_optimizations()
        
        # 显示优化配置
        config = create_low_memory_config()
        print("\n推荐配置:")
        print(f"- 表格大小限制: {config['tablerag']['max_table_size']} 行")
        print(f"- 内容长度限制: {config['tablerag']['max_content_length']} 字符")
        print(f"- 批处理大小: {config['tablerag']['batch_size']}")
        print(f"- 块大小: {config['processing']['chunk_size']}")
        
        # 显示内存信息
        info = memory_optimizer.get_memory_info()
        print(f"\n当前内存状态:")
        print(f"- CPU: {info['cpu_memory']['percent']:.1f}% ({info['cpu_memory']['available']:.1f}GB 可用)")
        if "gpu_memory" in info and "total" in info["gpu_memory"]:
            print(f"- GPU: {info['gpu_memory']['allocated']:.2f}GB / {info['gpu_memory']['total']:.1f}GB")
    else:
        print("❌ 系统不兼容，请升级硬件或使用CPU模式")
