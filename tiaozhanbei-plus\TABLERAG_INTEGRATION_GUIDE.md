# TableRAG集成使用指南

## 概述

本指南介绍如何在现有的RAG系统中集成TableRAG功能，以增强Excel表格数据的处理和问答能力。

## 功能特性

### 🚀 核心功能
- **智能表格过滤**: 基于语义和LLM的表格内容过滤
- **表格澄清**: 自动生成术语解释和表格摘要
- **增强检索**: ColBERT驱动的精确表格检索
- **多格式支持**: 支持Markdown、HTML等多种表格格式
- **混合处理**: 同时支持传统和TableRAG处理模式

### 📊 处理模式
1. **Simple模式**: 传统的行级文本处理
2. **TableRAG模式**: 完整的TableRAG增强处理
3. **Hybrid模式**: 结合两种方法的优势

## 安装和配置

### 1. 安装依赖

```bash
# 运行自动安装脚本
python install_tablerag.py

# 或手动安装依赖
pip install -r tablerag_requirements.txt
```

### 2. 验证安装

```bash
# 验证集成配置
python validate_tablerag_setup.py

# 测试功能
python test_tablerag_integration.py
```

### 3. 配置系统

在`app.py`中的配置已经自动更新：

```python
class Config:
    # TableRAG配置
    USE_TABLERAG = TABLERAG_AVAILABLE  # 是否启用TableRAG
    TABLERAG_CONFIG_NAME = "enhanced"  # 配置模式
    EXCEL_PROCESSING_MODE = "hybrid"   # 处理模式
```

## 配置选项

### TableRAG配置模式

1. **basic**: 基础模式，最小功能集
2. **enhanced**: 增强模式，完整功能（推荐）
3. **performance**: 性能模式，优化速度
4. **minimal**: 最小模式，仅基本处理

### Excel处理模式

1. **simple**: 仅使用传统处理
2. **tablerag**: 仅使用TableRAG处理
3. **hybrid**: 同时使用两种方法（推荐）

## 使用方法

### 1. 重建知识库

集成TableRAG后，需要重建知识库：

```bash
python build_knowledge_base.py
```

### 2. 启动应用

```bash
streamlit run app.py
```

### 3. 使用表格问答

现在你可以：
- 上传Excel文件到`./data`目录
- 询问关于表格内容的问题
- 享受增强的表格理解和回答能力

## 配置文件

### tablerag_config.json

```json
{
  "enabled": true,
  "table_filter_name": "llm_based_filter",
  "table_clarifier_name": "term_explanations_and_table_summary",
  "table_format": "markdown",
  "use_table_filter": true,
  "max_table_size": 1000,
  "enable_cache": true
}
```

### 配置参数说明

- `enabled`: 是否启用TableRAG功能
- `table_filter_name`: 表格过滤器类型
  - `"llm_based_filter"`: LLM过滤器
  - `"semetics_based_filter"`: 语义过滤器
  - `"None"`: 不使用过滤器
- `table_clarifier_name`: 表格澄清器类型
  - `"term_explanations_and_table_summary"`: 术语解释+表格摘要
  - `"term_explanations"`: 仅术语解释
  - `"table_summary"`: 仅表格摘要
  - `"None"`: 不使用澄清器
- `table_format`: 表格格式
  - `"markdown"`: Markdown格式
  - `"html"`: HTML格式
  - `"string"`: 字符串格式

## 性能优化

### 1. 缓存配置

```python
# 启用缓存以提高性能
config.enable_cache = True
config.cache_dir = "./tablerag_cache"
```

### 2. 并行处理

```python
# 启用并行处理
config.parallel_processing = True
config.max_workers = 4
```

### 3. 表格大小限制

```python
# 限制处理的表格大小
config.max_table_size = 1000  # 最大行数
config.max_content_length = 5000  # 最大内容长度
```

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'tablerag_adapter'
   ```
   **解决**: 确保所有文件在正确位置，运行`python validate_tablerag_setup.py`

2. **TableRAG组件初始化失败**
   ```
   TableRAG组件初始化失败: ...
   ```
   **解决**: 检查`tablerag-main`目录是否完整，确保依赖正确安装

3. **向量存储错误**
   ```
   TableRAG向量存储增强器初始化失败
   ```
   **解决**: 检查嵌入模型是否正确加载

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 回退机制

系统具有完整的回退机制：
- TableRAG失败时自动回退到简单处理
- 组件初始化失败时使用标准组件
- 确保系统始终可用

## 最佳实践

### 1. 表格数据准备
- 确保Excel文件格式正确
- 使用清晰的列名
- 避免过大的表格（>1000行）

### 2. 查询优化
- 使用具体的表格相关问题
- 包含表格中的关键词
- 避免过于抽象的查询

### 3. 性能监控
- 定期检查缓存使用情况
- 监控处理时间
- 根据需要调整配置

## 扩展开发

### 自定义过滤器

```python
from tablerag_adapter import TableRAGAdapter

class CustomTableRAGAdapter(TableRAGAdapter):
    def _apply_custom_filter(self, table_data):
        # 实现自定义过滤逻辑
        pass
```

### 自定义澄清器

```python
def custom_clarifier(table_data):
    # 实现自定义澄清逻辑
    return {"custom_info": "..."}
```

## 更新和维护

### 更新TableRAG

```bash
# 更新TableRAG组件
cd tablerag-main
git pull origin main

# 重新验证
python validate_tablerag_setup.py
```

### 清理缓存

```bash
# 清理TableRAG缓存
rm -rf tablerag_cache/*

# 重建知识库
python build_knowledge_base.py
```

## 支持和反馈

如果遇到问题：

1. 首先运行验证脚本：`python validate_tablerag_setup.py`
2. 查看日志文件：`./logs/tablerag.log`
3. 检查配置文件：`tablerag_config.json`
4. 运行测试脚本：`python test_tablerag_integration.py`

## 版本信息

- TableRAG集成版本: 1.0.0
- 支持的Python版本: 3.8+
- 依赖的主要包: transformers>=4.36.2, pandas>=1.5.0

---

**注意**: 首次使用TableRAG功能时，系统会下载必要的模型文件，这可能需要一些时间。请确保网络连接正常。
