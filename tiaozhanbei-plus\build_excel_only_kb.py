# build_excel_only_kb.py
"""
仅构建Excel知识库的脚本
跳过PDF处理，专注测试TableRAG功能
"""

import os
import shutil
from pathlib import Path
import chromadb
from llama_index.core import VectorStoreIndex, StorageContext, Settings
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.vector_stores.chroma import ChromaVectorStore

# 导入应用模块
from app import (
    Config,
    load_excels_enhanced,
    create_nodes_from_excel,
    init_node_parser,
    init_models
)


def build_excel_knowledge_base():
    """构建仅包含Excel数据的知识库"""
    print("=== 构建Excel专用知识库 ===")
    print("跳过PDF处理，专注测试TableRAG功能")
    
    try:
        # 1. 初始化模型
        print("\n步骤 1/6: 初始化模型...")
        embed_model, llm, rerank_model = init_models()
        Settings.embed_model = embed_model
        Settings.llm = llm
        print("✓ 模型初始化完成")
        
        # 2. 初始化节点解析器
        print("\n步骤 2/6: 初始化节点解析器...")
        node_parser = init_node_parser()
        print("✓ 节点解析器初始化完成")
        
        # 3. 清理旧的知识库
        print("\n步骤 3/6: 清理旧的知识库...")
        db_dir = Path(Config.VECTOR_DB_DIR)
        persist_dir = Path(Config.PERSIST_DIR)
        
        if db_dir.exists():
            shutil.rmtree(db_dir)
            print("✓ 清理向量数据库目录")
        
        if persist_dir.exists():
            shutil.rmtree(persist_dir)
            print("✓ 清理持久化目录")
        
        # 4. 加载Excel数据
        print(f"\n步骤 4/6: 从 {Config.DATA_DIR} 加载Excel文件...")
        excel_files = list(Path(Config.DATA_DIR).rglob("*.xlsx"))
        print(f"发现 {len(excel_files)} 个Excel文件")
        
        if not excel_files:
            print("❌ 未找到Excel文件，请将Excel文件放入 ./data 目录")
            return False
        
        # 使用TableRAG增强处理
        excel_data = load_excels_enhanced(Config.DATA_DIR)
        print(f"✓ Excel数据加载完成，共 {len(excel_data)} 条记录")
        
        if not excel_data:
            print("❌ Excel数据为空，请检查文件内容")
            return False
        
        # 5. 创建节点
        print("\n步骤 5/6: 创建文本节点...")
        excel_nodes = create_nodes_from_excel(excel_data, node_parser)
        print(f"✓ 节点创建完成，共 {len(excel_nodes)} 个节点")
        
        if not excel_nodes:
            print("❌ 未能创建任何节点")
            return False
        
        # 6. 构建向量索引
        print("\n步骤 6/6: 构建向量索引...")
        
        # 创建向量数据库
        db_dir.mkdir(parents=True, exist_ok=True)
        db = chromadb.PersistentClient(path=str(db_dir))
        chroma_collection = db.get_or_create_collection(Config.COLLECTION_NAME)
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        
        # 创建存储上下文
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        
        # 构建索引
        print("正在构建向量索引...")
        index = VectorStoreIndex(
            nodes=excel_nodes,
            storage_context=storage_context,
            show_progress=True
        )
        
        # 持久化存储
        persist_dir.mkdir(parents=True, exist_ok=True)
        index.storage_context.persist(persist_dir=str(persist_dir))
        
        print("✓ 向量索引构建完成")
        print(f"✓ 数据已持久化到 {persist_dir}")
        
        # 显示统计信息
        print("\n=== 知识库统计 ===")
        print(f"Excel文件数: {len(excel_files)}")
        print(f"数据记录数: {len(excel_data)}")
        print(f"文本节点数: {len(excel_nodes)}")
        print(f"向量维度: {embed_model.embed_dim if hasattr(embed_model, 'embed_dim') else '未知'}")
        
        # 显示节点类型分布
        node_types = {}
        for node in excel_nodes:
            node_type = node.metadata.get('processing_method', 'unknown')
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        print("\n节点类型分布:")
        for node_type, count in node_types.items():
            print(f"  - {node_type}: {count} 个")
        
        print("\n🎉 Excel知识库构建成功！")
        print("\n下一步:")
        print("1. 运行: streamlit run app.py")
        print("2. 测试Excel表格问答功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 知识库构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_excel_knowledge_base():
    """测试Excel知识库"""
    print("\n=== 测试Excel知识库 ===")
    
    try:
        from app import load_knowledge_base
        
        # 加载知识库
        print("加载知识库...")
        index = load_knowledge_base()
        
        if index is None:
            print("❌ 知识库加载失败")
            return False
        
        print("✓ 知识库加载成功")
        
        # 创建查询引擎
        query_engine = index.as_query_engine(similarity_top_k=3)
        
        # 测试查询
        test_queries = [
            "表格中有什么内容？",
            "有多少行数据？",
            "表格的列名是什么？"
        ]
        
        print("\n测试查询:")
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. 查询: {query}")
            try:
                response = query_engine.query(query)
                print(f"   回答: {response}")
            except Exception as e:
                print(f"   错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("Excel专用知识库构建工具")
    print("=" * 50)
    
    # 检查数据目录
    data_dir = Path(Config.DATA_DIR)
    if not data_dir.exists():
        print(f"❌ 数据目录不存在: {data_dir}")
        print("请创建数据目录并放入Excel文件")
        return
    
    excel_files = list(data_dir.rglob("*.xlsx"))
    if not excel_files:
        print(f"❌ 在 {data_dir} 中未找到Excel文件")
        print("请将Excel文件放入数据目录")
        return
    
    print(f"✓ 发现 {len(excel_files)} 个Excel文件:")
    for file in excel_files:
        print(f"  - {file.name}")
    
    # 构建知识库
    success = build_excel_knowledge_base()
    
    if success:
        # 测试知识库
        print("\n" + "=" * 50)
        test_success = test_excel_knowledge_base()
        
        if test_success:
            print("\n🚀 一切就绪！现在可以启动应用:")
            print("streamlit run app.py")
        else:
            print("\n⚠️ 知识库构建成功，但测试有问题")
            print("请检查配置或直接启动应用测试")
    else:
        print("\n❌ 知识库构建失败")
        print("请检查错误信息并修复问题")


if __name__ == "__main__":
    main()
