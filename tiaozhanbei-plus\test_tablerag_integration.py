# test_tablerag_integration.py
"""
TableRAG集成测试脚本
测试TableRAG功能是否正常工作
"""

import os
import sys
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
import tempfile
import shutil


def create_test_excel_file(file_path: str) -> bool:
    """创建测试Excel文件"""
    try:
        # 创建测试数据
        data = {
            '项目名称': ['南水北调东线工程', '南水北调中线工程', '南水北调西线工程'],
            '建设状态': ['已完工', '已完工', '规划中'],
            '输水能力': ['148亿立方米/年', '95亿立方米/年', '170亿立方米/年'],
            '主要受益区域': ['华北、胶东', '华北、西北', '西北、西南'],
            '投资金额': ['1420亿元', '2013亿元', '预计4000亿元']
        }
        
        df = pd.DataFrame(data)
        df.to_excel(file_path, index=False, sheet_name='南水北调工程概况')
        
        print(f"✓ 测试Excel文件创建成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"✗ 创建测试Excel文件失败: {e}")
        return False


def test_tablerag_adapter():
    """测试TableRAG适配器"""
    print("\n=== 测试TableRAG适配器 ===")
    
    try:
        from tablerag_adapter import create_tablerag_adapter
        from tablerag_config import get_tablerag_config
        
        # 创建配置
        config = get_tablerag_config("basic")
        print("✓ TableRAG配置创建成功")
        
        # 创建适配器
        adapter = create_tablerag_adapter(config.to_dict())
        print("✓ TableRAG适配器创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ TableRAG适配器测试失败: {e}")
        return False


def test_excel_processing():
    """测试Excel处理功能"""
    print("\n=== 测试Excel处理功能 ===")
    
    try:
        # 创建临时目录和测试文件
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test_data.xlsx"
            
            # 创建测试Excel文件
            if not create_test_excel_file(str(test_file)):
                return False
            
            # 测试简单处理
            from app import _load_excels_simple
            simple_data = _load_excels_simple(temp_dir)
            print(f"✓ 简单Excel处理成功，生成 {len(simple_data)} 条数据")
            
            # 测试TableRAG处理（如果可用）
            try:
                from app import _load_excels_with_tablerag
                tablerag_data = _load_excels_with_tablerag(temp_dir)
                print(f"✓ TableRAG Excel处理成功，生成 {len(tablerag_data)} 条数据")
            except Exception as e:
                print(f"⚠ TableRAG Excel处理失败: {e}")
            
            # 测试混合处理
            try:
                from app import _load_excels_hybrid
                hybrid_data = _load_excels_hybrid(temp_dir)
                print(f"✓ 混合Excel处理成功，生成 {len(hybrid_data)} 条数据")
            except Exception as e:
                print(f"⚠ 混合Excel处理失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Excel处理测试失败: {e}")
        return False


def test_node_creation():
    """测试节点创建功能"""
    print("\n=== 测试节点创建功能 ===")
    
    try:
        from app import create_nodes_from_excel, init_node_parser
        
        # 创建测试数据
        test_data = [
            {
                "content": "测试表格数据 - 文件: test.xlsx, 工作表: Sheet1, 行号: 2. 内容: {'项目': '南水北调', '状态': '已完工'}",
                "metadata": {
                    "source_file": "test.xlsx",
                    "content_type": "excel_row",
                    "sheet_name": "Sheet1",
                    "row_number": 2
                }
            },
            {
                "content": "TableRAG增强表格数据...",
                "metadata": {
                    "source_file": "test.xlsx",
                    "content_type": "excel_table",
                    "processing_method": "tablerag",
                    "sheet_name": "Sheet1"
                }
            }
        ]
        
        # 初始化节点解析器
        node_parser = init_node_parser()
        
        # 创建节点
        nodes = create_nodes_from_excel(test_data, node_parser)
        print(f"✓ 节点创建成功，生成 {len(nodes)} 个节点")
        
        # 检查节点类型
        traditional_nodes = sum(1 for node in nodes if node.metadata.get("content_type") == "excel_row")
        tablerag_nodes = sum(1 for node in nodes if node.metadata.get("processing_method") == "tablerag")
        
        print(f"  - 传统节点: {traditional_nodes}")
        print(f"  - TableRAG节点: {tablerag_nodes}")
        
        return True
        
    except Exception as e:
        print(f"✗ 节点创建测试失败: {e}")
        return False


def test_retriever_integration():
    """测试检索器集成"""
    print("\n=== 测试检索器集成 ===")
    
    try:
        from tablerag_retriever import create_tablerag_query_processor
        from tablerag_config import get_tablerag_config
        
        # 创建配置
        config = get_tablerag_config("basic")
        
        # 创建查询处理器
        processor = create_tablerag_query_processor(config)
        print("✓ TableRAG查询处理器创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 检索器集成测试失败: {e}")
        return False


def test_vector_enhancement():
    """测试向量增强功能"""
    print("\n=== 测试向量增强功能 ===")
    
    try:
        from tablerag_vector_enhancer import create_tablerag_vector_enhancer
        from llama_index.embeddings.huggingface import HuggingFaceEmbedding
        from llama_index.core.schema import TextNode
        
        # 创建嵌入模型（使用简单模型进行测试）
        try:
            embed_model = HuggingFaceEmbedding(model_name="sentence-transformers/all-MiniLM-L6-v2")
        except:
            print("⚠ 无法加载嵌入模型，跳过向量增强测试")
            return True
        
        # 创建向量增强器
        enhancer = create_tablerag_vector_enhancer(embed_model)
        print("✓ TableRAG向量增强器创建成功")
        
        # 创建测试节点
        test_node = TextNode(
            text="测试表格内容",
            id_="test_node_1",
            metadata={
                "content_type": "excel_table",
                "processing_method": "tablerag",
                "source_file": "test.xlsx"
            }
        )
        
        # 测试节点增强
        enhanced_nodes = enhancer.enhance_table_nodes_for_indexing([test_node])
        print(f"✓ 节点增强成功，处理 {len(enhanced_nodes)} 个节点")
        
        return True
        
    except Exception as e:
        print(f"✗ 向量增强测试失败: {e}")
        return False


def test_configuration():
    """测试配置功能"""
    print("\n=== 测试配置功能 ===")
    
    try:
        from tablerag_config import (
            get_tablerag_config, 
            save_tablerag_config, 
            create_tablerag_config_from_file,
            TABLERAG_CONFIGS
        )
        
        # 测试预定义配置
        for config_name in TABLERAG_CONFIGS.keys():
            config = get_tablerag_config(config_name)
            if config.validate():
                print(f"✓ 配置 '{config_name}' 验证通过")
            else:
                print(f"✗ 配置 '{config_name}' 验证失败")
        
        # 测试配置保存和加载
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_config_file = f.name
        
        try:
            config = get_tablerag_config("enhanced")
            save_success = save_tablerag_config(config, temp_config_file)
            
            if save_success:
                print("✓ 配置保存成功")
                
                loaded_config = create_tablerag_config_from_file(temp_config_file)
                if loaded_config:
                    print("✓ 配置加载成功")
                else:
                    print("✗ 配置加载失败")
            else:
                print("✗ 配置保存失败")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_config_file):
                os.unlink(temp_config_file)
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False


def run_comprehensive_test():
    """运行综合测试"""
    print("TableRAG集成功能测试")
    print("=" * 50)
    
    tests = [
        ("TableRAG适配器", test_tablerag_adapter),
        ("Excel处理功能", test_excel_processing),
        ("节点创建功能", test_node_creation),
        ("检索器集成", test_retriever_integration),
        ("向量增强功能", test_vector_enhancement),
        ("配置功能", test_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed / total) * 100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！TableRAG集成成功！")
        return True
    elif passed >= total * 0.7:
        print("\n⚠ 大部分测试通过，但有一些问题需要解决")
        return False
    else:
        print("\n❌ 多个测试失败，需要检查集成配置")
        return False


def main():
    """主函数"""
    success = run_comprehensive_test()
    
    if not success:
        print("\n建议:")
        print("1. 运行 python validate_tablerag_setup.py 检查配置")
        print("2. 确保所有依赖正确安装")
        print("3. 检查tablerag-main目录是否完整")
        print("4. 查看具体错误信息并修复")
        sys.exit(1)
    else:
        print("\n🚀 可以开始使用TableRAG增强的表格问答功能了！")


if __name__ == "__main__":
    main()
