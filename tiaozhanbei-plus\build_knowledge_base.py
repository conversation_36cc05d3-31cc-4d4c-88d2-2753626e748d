# build_knowledge_base.py
import os
import shutil
import time
from pathlib import Path
import chromadb
from tqdm import tqdm  # 添加进度条支持

# --- 环境变量设置 ---
# 设置环境变量，让MinerU从国内镜像下载模型（如果本地没有）
os.environ['MINERU_MODEL_SOURCE'] = "modelscope"
# 直接指定包含模型文件的正确本地目录
os.environ['MINERU_MODEL_DIR'] = r"D:\pythonProject\tiaozhanbei-plus\mineru-model"


from app import (
    Config,
    load_pdfs,
    load_excels_enhanced,
    create_nodes_from_pdf,
    create_nodes_from_excel,
    init_node_parser,
    init_models
)
from llama_index.core import VectorStoreIndex, StorageContext
from llama_index.vector_stores.chroma import ChromaVectorStore


def main_build():
    """
    一个专门用于构建和持久化知识库的脚本。
    添加进度条和更强大的错误处理。
    """
    print("--- 开始构建知识库 ---")

    try:
        # 0. 初始化必要的模型和解析器
        print("步骤 0/5: 初始化模型...")
        init_models()  # 这会设置 Settings.embed_model
        node_parser = init_node_parser()
        print("模型初始化完成。")
        
        # 输出节点解析器类型信息
        parser_type = node_parser.__class__.__name__
        print(f"使用的节点解析器类型: {parser_type}")
        if "Semantic" in parser_type:
            print("已启用语义感知分块，将根据文本内容自动调整块大小")

        # 1. 清理旧的数据库和存储目录
        print("步骤 1/5: 清理旧的知识库目录...")
        if Path(Config.VECTOR_DB_DIR).exists():
            shutil.rmtree(Config.VECTOR_DB_DIR)
            print(f"已删除旧的向量数据库目录: {Config.VECTOR_DB_DIR}")
        if Path(Config.PERSIST_DIR).exists():
            shutil.rmtree(Config.PERSIST_DIR)
            print(f"已删除旧的持久化存储目录: {Config.PERSIST_DIR}")
        print("清理完成。")

        # 2. 加载和处理数据源，添加进度显示
        print("步骤 2/5: 从 ./data 目录加载原始文件...")
        
        # 首先计数需要处理的文件
        pdf_files = list(Path(Config.DATA_DIR).rglob("*.pdf"))
        excel_files = list(Path(Config.DATA_DIR).rglob("*.xlsx"))
        print(f"发现 {len(pdf_files)} 个PDF文件和 {len(excel_files)} 个Excel文件")
        
        pdf_data = load_pdfs(Config.DATA_DIR)
        # 使用增强的Excel加载函数
        excel_data = load_excels_enhanced(Config.DATA_DIR) if Config.USE_TABLERAG else []
        print(f"文件加载完成: PDF数据 {len(pdf_data)} 条, Excel数据 {len(excel_data)} 条")

        if not pdf_data and not excel_data: # 修改判断条件
            print("警告: 未加载到任何PDF或Excel数据，请检查数据目录")

        # 3. 从数据创建节点，添加进度显示
        print("步骤 3/5: 创建文本节点...")
        print("处理PDF数据...")
        pdf_nodes = create_nodes_from_pdf(pdf_data, node_parser)
        print("处理Excel数据...")
        excel_nodes = create_nodes_from_excel(excel_data, node_parser) if excel_data else []
        nodes = pdf_nodes + excel_nodes
        print(f"节点创建完成，共 {len(nodes)} 个节点（PDF: {len(pdf_nodes)}, Excel: {len(excel_nodes)}）")
        
        # 分析节点文本长度分布
        if nodes:
            lengths = [len(node.text) for node in nodes]
            avg_length = sum(lengths) / len(lengths)
            min_length = min(lengths)
            max_length = max(lengths)
            print(f"节点文本长度统计: 平均 {avg_length:.1f} 字符, 最小 {min_length} 字符, 最大 {max_length} 字符")
        
        if not nodes:
            print("错误：没有找到任何可处理的节点，程序终止。")
            return

        # 4. 创建新的向量存储并构建索引
        print("步骤 4/5: 开始生成嵌入并构建索引（此过程可能需要较长时间）...")
        start_time = time.time()
        db = chromadb.PersistentClient(path=Config.VECTOR_DB_DIR)
        chroma_collection = db.get_or_create_collection(Config.COLLECTION_NAME)
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # 显式地将节点添加到文档存储中
        print(f"添加 {len(nodes)} 个节点到文档存储...")
        storage_context.docstore.add_documents(nodes)

        # 添加进度显示
        print("构建向量索引（可能需要几分钟到几小时，取决于数据量）...")
        index = VectorStoreIndex(
            nodes, storage_context=storage_context, show_progress=True
        )
        end_time = time.time()
        print(f"嵌入生成和索引构建完成，耗时: {end_time - start_time:.2f} 秒。")

        # 5. 持久化到磁盘
        print("步骤 5/5: 将知识库持久化到磁盘...")
        # 持久化所有组件，包括新填充的docstore
        index.storage_context.persist(persist_dir=Config.PERSIST_DIR)
        print("--- 知识库构建成功并已保存！---")
        print(f"路径: {Path(Config.PERSIST_DIR).absolute()}")
        
        # 打印统计信息
        print("\n--- 知识库统计 ---")
        print(f"处理的PDF文件数: {len(pdf_files)}")
        print(f"找到的Excel文件数: {len(excel_files)} (将由Agent在运行时动态查询)")
        print(f"创建的PDF节点数: {len(pdf_nodes)}")
        # print(f"创建的Excel节点数: {len(excel_nodes)}")
        print(f"总索引节点数: {len(nodes)}")
        print(f"分块方式: {parser_type}")
        
    except Exception as e:
        print(f"构建知识库时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        print("请检查数据文件和依赖项后重试。")


if __name__ == "__main__":
    main_build()
