{"enabled": true, "fallback_to_simple": true, "embedding_type": "text-embedding-3-large", "table_filter_name": "llm_based_filter", "table_clarifier_name": "term_explanations_and_table_summary", "table_format": "markdown", "top_k": 5, "use_table_filter": true, "use_self_consistency": false, "max_table_size": 1000, "max_content_length": 5000, "parallel_processing": true, "max_workers": 4, "enable_cache": true, "cache_dir": "./tablerag_cache", "api_key": "", "api_base": "https://api.openai.com/v1", "model_name": "gpt-4o-mini", "log_level": "INFO", "log_file": "./logs/tablerag.log"}