# final_test.py
"""
最终测试修复后的Pandas查询功能
"""

def test_output_processor_with_problematic_code():
    """测试输出处理器处理有问题的代码"""
    print("=== 测试输出处理器 ===")
    
    # 模拟之前出错的输出
    problematic_outputs = [
        # 包含try-except的复杂代码
        """
```
try:
    start_time = '2020-01-01 12'
    end_time = '2020-01-02 12'
    time_range = df[(df['时间'] >= start_time) & (df['时间'] <= end_time)]
    if not time_range.empty:
        print(time_range)
    else:
        print(f'时间范围: {df["时间"].min()} 至 {df["时间"].max()}')
        print('未找到指定时间范围内的数据')
except:
    print(f'数据形状: {df.shape}')
    print(df.head())
```
        """,
        
        # 包含markdown和解释的输出
        """
根据您的查询，我将显示数据的基本信息。

```python
print(df.head())
print(df.shape)
```

这将显示前5行数据和数据的形状。
        """,
        
        # 空输出
        "",
        
        # 只有解释没有代码
        "根据提供的数据，找不到符合条件的结果。"
    ]
    
    try:
        from app import custom_output_processor
        
        success_count = 0
        for i, output in enumerate(problematic_outputs, 1):
            print(f"\n--- 测试用例 {i} ---")
            print(f"原始输出: {output[:100]}{'...' if len(output) > 100 else ''}")
            
            try:
                cleaned = custom_output_processor(output, f"测试查询{i}")
                print(f"处理结果: {cleaned[:100]}{'...' if len(cleaned) > 100 else ''}")
                
                # 检查结果
                if cleaned and "print(" in cleaned and "```" not in cleaned:
                    print("✓ 处理成功")
                    success_count += 1
                else:
                    print("⚠️ 处理结果可能有问题")
                    
            except Exception as e:
                print(f"✗ 处理失败: {e}")
        
        print(f"\n输出处理器测试: {success_count}/{len(problematic_outputs)} 成功")
        return success_count == len(problematic_outputs)
        
    except Exception as e:
        print(f"✗ 输出处理器测试失败: {e}")
        return False


def test_simple_queries():
    """测试简单查询"""
    print("\n=== 测试简单查询 ===")
    
    try:
        from app import load_knowledge_base
        
        # 加载知识库
        print("加载知识库...")
        index = load_knowledge_base()
        
        if index is None:
            print("❌ 知识库加载失败")
            return False
        
        print("✓ 知识库加载成功")
        
        # 创建查询引擎
        query_engine = index.as_query_engine(similarity_top_k=3)
        
        # 测试简单查询
        simple_queries = [
            "这个表格有多少行数据？",
            "显示前3行数据",
            "表格的列名是什么？",
        ]
        
        success_count = 0
        for query in simple_queries:
            print(f"\n查询: {query}")
            try:
                response = query_engine.query(query)
                response_str = str(response)
                
                if len(response_str) > 10 and "error" not in response_str.lower():
                    print(f"✓ 查询成功: {response_str[:100]}...")
                    success_count += 1
                else:
                    print(f"⚠️ 查询结果可能有问题: {response_str}")
                    
            except Exception as e:
                print(f"✗ 查询失败: {e}")
        
        print(f"\n简单查询测试: {success_count}/{len(simple_queries)} 成功")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 简单查询测试失败: {e}")
        return False


def test_data_exploration():
    """测试数据探索功能"""
    print("\n=== 测试数据探索 ===")
    
    try:
        from app import load_excel_dataframes, Config
        
        # 加载DataFrame
        dataframes = load_excel_dataframes(Config.DATA_DIR)
        
        if not dataframes:
            print("❌ 未加载到DataFrame")
            return False
        
        print(f"✓ 加载了 {len(dataframes)} 个DataFrame")
        
        # 测试每个DataFrame的基本操作
        for name, df in dataframes.items():
            print(f"\n📊 DataFrame: {name}")
            print(f"   形状: {df.shape}")
            print(f"   列名: {df.columns.tolist()[:5]}{'...' if len(df.columns) > 5 else ''}")
            
            # 测试基本操作
            try:
                print(f"   前3行数据:")
                print(df.head(3).to_string()[:200] + "...")
                print("✓ 数据访问正常")
            except Exception as e:
                print(f"✗ 数据访问失败: {e}")
                return False
            
            break  # 只测试第一个
        
        return True
        
    except Exception as e:
        print(f"❌ 数据探索测试失败: {e}")
        return False


def main():
    """主函数"""
    print("最终修复测试")
    print("=" * 50)
    
    # 测试输出处理器
    processor_success = test_output_processor_with_problematic_code()
    
    # 测试数据探索
    data_success = test_data_exploration()
    
    # 测试简单查询
    query_success = test_simple_queries()
    
    print("\n" + "=" * 50)
    print("最终测试结果:")
    print(f"输出处理器: {'✓ 成功' if processor_success else '✗ 失败'}")
    print(f"数据探索: {'✓ 成功' if data_success else '✗ 失败'}")
    print(f"简单查询: {'✓ 成功' if query_success else '✗ 失败'}")
    
    overall_success = processor_success and data_success and query_success
    
    if overall_success:
        print("\n🎉 所有测试通过！修复成功！")
        print("\n💡 现在可以启动应用:")
        print("streamlit run app.py")
        print("\n🔍 建议尝试的查询:")
        print("1. '这个表格有多少行数据？'")
        print("2. '显示前5行数据'")
        print("3. '表格的列名是什么？'")
        print("4. '数据的基本统计信息'")
        print("5. '数据的时间范围是什么？'")
    elif data_success:
        print("\n⚠️ 基础功能正常，但查询可能还有问题")
        print("建议先启动应用，尝试简单查询")
    else:
        print("\n❌ 还有基础问题需要解决")
        print("请检查数据文件和配置")


if __name__ == "__main__":
    main()
