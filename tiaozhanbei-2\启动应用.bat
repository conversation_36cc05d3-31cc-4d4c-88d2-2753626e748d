@echo off
chcp 65001 >nul
title 南水北调水利问答助手 - 启动器

echo.
echo ========================================
echo 🌊 南水北调水利问答助手 - 启动器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo    请先安装Python 3.8或更高版本
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 显示Python版本
echo 🔍 检查Python版本...
python --version

:: 检查是否在正确目录
if not exist "app.py" (
    echo.
    echo ❌ 错误: 未找到app.py文件
    echo    请确保在正确的项目目录下运行此脚本
    pause
    exit /b 1
)

echo.
echo 🚀 正在启动应用...
echo    如果是首次运行，可能需要较长时间加载模型
echo.

:: 运行启动脚本
python start_app.py

:: 如果出错，暂停以便查看错误信息
if errorlevel 1 (
    echo.
    echo ❌ 启动过程中出现错误
    pause
)
