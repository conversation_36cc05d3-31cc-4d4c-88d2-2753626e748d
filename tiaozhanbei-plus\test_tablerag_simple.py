# test_tablerag_simple.py
"""
TableRAG集成简化测试脚本
测试基本功能是否正常工作
"""

import os
import sys
import pandas as pd
from pathlib import Path
import tempfile


def test_basic_imports():
    """测试基本导入"""
    print("=== 测试基本导入 ===")
    
    try:
        # 测试配置导入
        from tablerag_config import get_tablerag_config
        config = get_tablerag_config("basic")
        print("✓ TableRAG配置导入成功")
        
        # 测试适配器导入（不依赖TableRAG组件）
        print("✓ 基本导入测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本导入测试失败: {e}")
        return False


def test_excel_processing():
    """测试Excel处理功能"""
    print("\n=== 测试Excel处理功能 ===")
    
    try:
        # 创建测试数据
        data = {
            '项目名称': ['南水北调东线工程', '南水北调中线工程'],
            '建设状态': ['已完工', '已完工'],
            '输水能力': ['148亿立方米/年', '95亿立方米/年']
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = Path(temp_dir) / "test.xlsx"
            df = pd.DataFrame(data)
            df.to_excel(test_file, index=False)
            print("✓ 测试Excel文件创建成功")
            
            # 测试简单Excel处理
            from app import _load_excels_simple
            simple_data = _load_excels_simple(temp_dir)
            print(f"✓ 简单Excel处理成功，生成 {len(simple_data)} 条数据")
            
            return True
            
    except Exception as e:
        print(f"✗ Excel处理测试失败: {e}")
        return False


def test_configuration():
    """测试配置功能"""
    print("\n=== 测试配置功能 ===")
    
    try:
        from tablerag_config import get_tablerag_config, TABLERAG_CONFIGS
        
        # 测试所有预定义配置
        for config_name in TABLERAG_CONFIGS.keys():
            config = get_tablerag_config(config_name)
            if config.validate():
                print(f"✓ 配置 '{config_name}' 验证通过")
            else:
                print(f"✗ 配置 '{config_name}' 验证失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False


def test_app_integration():
    """测试应用集成"""
    print("\n=== 测试应用集成 ===")
    
    try:
        # 检查app.py中的关键函数
        from app import Config
        
        # 检查TableRAG配置
        if hasattr(Config, 'USE_TABLERAG'):
            print("✓ app.py包含USE_TABLERAG配置")
        else:
            print("✗ app.py缺少USE_TABLERAG配置")
            return False
        
        if hasattr(Config, 'EXCEL_PROCESSING_MODE'):
            print("✓ app.py包含EXCEL_PROCESSING_MODE配置")
        else:
            print("✗ app.py缺少EXCEL_PROCESSING_MODE配置")
            return False
        
        # 检查增强Excel加载函数
        from app import load_excels_enhanced
        print("✓ load_excels_enhanced函数导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 应用集成测试失败: {e}")
        return False


def check_dependencies():
    """检查依赖"""
    print("\n=== 检查依赖 ===")
    
    required_modules = [
        'pandas',
        'numpy',
        'openpyxl',
        'transformers',
        'sentence_transformers'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} 已安装")
        except ImportError:
            print(f"✗ {module} 未安装")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n缺失的模块: {', '.join(missing_modules)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("TableRAG集成简化测试")
    print("=" * 50)
    
    tests = [
        ("依赖检查", check_dependencies),
        ("基本导入", test_basic_imports),
        ("Excel处理功能", test_excel_processing),
        ("配置功能", test_configuration),
        ("应用集成", test_app_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {(passed / total) * 100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有基本测试通过！")
        print("\n下一步:")
        print("1. 运行 python install_missing_deps.py 安装缺失依赖")
        print("2. 运行 python build_knowledge_base.py 重建知识库")
        print("3. 启动应用: streamlit run app.py")
        return True
    elif passed >= total * 0.6:
        print("\n⚠ 大部分测试通过，但有一些问题需要解决")
        print("\n建议:")
        print("1. 安装缺失的依赖")
        print("2. 检查错误信息并修复")
        return False
    else:
        print("\n❌ 多个基本测试失败")
        print("\n建议:")
        print("1. 确保Python环境正确")
        print("2. 安装所需依赖")
        print("3. 检查文件是否完整")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
