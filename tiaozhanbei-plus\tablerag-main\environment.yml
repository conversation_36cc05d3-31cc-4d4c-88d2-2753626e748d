name: tablerag
channels:
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotli-python=1.0.9=py310h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.7.2=h06a4308_0
  - certifi=2024.7.4=py310h06a4308_0
  - cuda-cudart=12.1.105=0
  - cuda-cupti=12.1.105=0
  - cuda-libraries=12.1.0=0
  - cuda-nvrtc=12.1.105=0
  - cuda-nvtx=12.1.105=0
  - cuda-opencl=12.5.39=0
  - cuda-runtime=12.1.0=0
  - cuda-version=12.5=3
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.12.1=h4a9f257_0
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py310heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - jpeg=9e=h5eee18b_2
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libcublas=*********=0
  - libcufft=********=0
  - libcufile=********=0
  - libcurand=*********=0
  - libcusolver=*********=0
  - libcusparse=*********=0
  - libdeflate=1.17=h5eee18b_1
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=12.0.2.50=0
  - libnvjitlink=12.1.105=0
  - libnvjpeg=12.1.1.14=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libwebp-base=1.3.2=h5eee18b_0
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py310h5eee18b_1
  - mkl_fft=1.3.8=py310h5eee18b_0
  - mkl_random=1.2.4=py310hdb19cb5_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpmath=1.3.0=py310h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.7.3=hbbd107a_1
  - numpy-base=1.26.4=py310hb5e798b_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.4.0=h9ca470c_2
  - openssl=3.0.14=h5eee18b_0
  - pip=24.0=py310h06a4308_0
  - pysocks=1.7.1=py310h06a4308_0
  - python=3.10.14=h955ad1f_1
  - pytorch=2.3.1=py3.10_cuda12.1_cudnn8.9.2_0
  - pytorch-cuda=12.1=ha16c6d3_5
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.1=py310h5eee18b_0
  - readline=8.2=h5eee18b_0
  - setuptools=69.5.1=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - sympy=1.12=py310h06a4308_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.14=h39e8969_0
  - torchtriton=2.3.1=py310
  - typing_extensions=4.11.0=py310h06a4308_0
  - wheel=0.43.0=py310h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.5=hc292b87_2
  - pip:
      - aiohttp==3.9.5
      - aiosignal==1.3.1
      - annotated-types==0.7.0
      - anyio==3.6.2
      - appdirs==1.4.4
      - argon2-cffi==21.3.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.2.3
      - asttokens==2.4.1
      - async-generator==1.10
      - async-lru==2.0.4
      - async-timeout==4.0.2
      - attrs==22.2.0
      - babel==2.15.0
      - beautifulsoup4==4.12.3
      - bitarray==2.9.2
      - bleach==6.0.0
      - blinker==1.8.2
      - blis==0.7.9
      - bs4==0.0.1
      - build==0.10.0
      - cachecontrol==0.12.11
      - cachetools==5.3.1
      - catalogue==2.0.8
      - cffi==1.15.1
      - chardet==4.0.0
      - charset-normalizer==2.1.1
      - cleo==2.0.1
      - click==8.1.3
      - cloudpathlib==0.18.1
      - colbert-ai==0.2.19
      - comm==0.2.2
      - confection==0.1.0
      - contourpy==1.0.7
      - crashtest==0.4.1
      - cryptography==42.0.8
      - cssselect==1.2.0
      - cycler==0.11.0
      - cymem==2.0.7
      - dataclasses-json==0.5.7
      - datasets==2.11.0
      - datedelta==1.4
      - debugpy==1.8.2
      - decorator==5.1.1
      - defusedxml==0.7.1
      - deprecated==1.2.14
      - dill==0.3.6
      - dirtyjson==1.0.8
      - diskcache==5.6.1
      - distlib==0.3.6
      - distro==1.9.0
      - docker-pycreds==0.4.0
      - docopt==0.6.2
      - dulwich==0.21.3
      - emoji==1.7.0
      - en-core-web-sm==3.7.1
      - et-xmlfile==1.1.0
      - evaluate==0.4.0
      - exceptiongroup==1.1.1
      - executing==2.0.1
      - faiss-cpu==1.7.4
      - fake-useragent==1.1.3
      - fast-pytorch-kmeans==0.2.0.1
      - fastjsonschema==2.16.3
      - filelock==3.11.0
      - flask==3.0.3
      - fonttools==4.39.3
      - fqdn==1.5.1
      - frozenlist==1.3.3
      - fsspec==2023.5.0
      - git-python==1.0.3
      - gitdb==4.0.10
      - gitpython==3.1.31
      - google-api-core==2.11.1
      - google-api-python-client==2.93.0
      - google-auth==2.22.0
      - google-auth-httplib2==0.1.0
      - googleapis-common-protos==1.59.1
      - googlesearch-python==1.2.3
      - gpt4all==1.0.5
      - grapheme==0.6.0
      - greenlet==2.0.2
      - h11==0.14.0
      - html5lib==1.1
      - httpcore==1.0.5
      - httplib2==0.22.0
      - httpx==0.27.0
      - huggingface-hub==0.24.3
      - idna==3.4
      - importlib-metadata==6.3.0
      - installer==0.7.0
      - ipykernel==6.29.5
      - ipython==8.26.0
      - ipython-genutils==0.2.0
      - ipywidgets==8.0.6
      - isoduration==20.11.0
      - itsdangerous==2.2.0
      - jaraco-classes==3.2.3
      - jedi==0.19.1
      - jeepney==0.8.0
      - jinja2==3.1.2
      - joblib==1.2.0
      - json5==0.9.25
      - jsonlines==3.1.0
      - jsonpatch==1.33
      - jsonpointer==2.3
      - jsonschema==4.17.3
      - jupyter==1.0.0
      - jupyter-client==8.6.2
      - jupyter-console==6.6.3
      - jupyter-core==5.7.2
      - jupyter-events==0.6.3
      - jupyter-lsp==2.2.5
      - jupyter-server==2.5.0
      - jupyter-server-terminals==0.4.4
      - jupyterlab==4.1.6
      - jupyterlab-pygments==0.2.2
      - jupyterlab-server==2.24.0
      - jupyterlab-widgets==3.0.7
      - keyring==23.13.1
      - kiwisolver==1.4.4
      - langchain==0.1.0
      - langchain-community==0.0.20
      - langchain-core==0.1.23
      - langchain-text-splitters==0.0.2
      - langcodes==3.3.0
      - langsmith==0.0.87
      - language-data==1.2.0
      - llama-cloud==0.0.11
      - llama-cpp-python==0.1.72
      - llama-index==0.10.58
      - llama-index-agent-openai==0.2.9
      - llama-index-cli==0.1.13
      - llama-index-core==0.10.58
      - llama-index-embeddings-openai==0.1.11
      - llama-index-indices-managed-llama-cloud==0.2.7
      - llama-index-legacy==0.9.48
      - llama-index-llms-openai==0.1.27
      - llama-index-multi-modal-llms-openai==0.1.8
      - llama-index-program-openai==0.1.7
      - llama-index-question-gen-openai==0.1.3
      - llama-index-readers-file==0.1.32
      - llama-index-readers-llama-parse==0.1.6
      - llama-parse==0.4.9
      - lockfile==0.12.2
      - lxml==4.9.2
      - marisa-trie==1.2.0
      - markdown-it-py==2.2.0
      - markupsafe==2.1.2
      - marshmallow==3.19.0
      - marshmallow-enum==1.5.1
      - matplotlib==3.7.1
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mistune==2.0.5
      - more-itertools==9.1.0
      - msgpack==1.0.5
      - multidict==6.0.4
      - multipledispatch==0.6.0
      - multiprocess==0.70.14
      - murmurhash==1.0.9
      - mypy-extensions==1.0.0
      - nbclassic==0.5.5
      - nbclient==0.7.3
      - nbconvert==7.3.1
      - nbformat==5.8.0
      - nest-asyncio==1.6.0
      - networkx==3.1
      - ninja==********
      - nltk==3.8.1
      - notebook==6.5.4
      - notebook-shim==0.2.2
      - numexpr==2.8.4
      - numpy==1.24.2
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.5.82
      - nvidia-nvtx-cu12==12.1.105
      - onnx==1.16.1
      - openai==1.14.0
      - openapi-schema-pydantic==1.2.4
      - openpyxl==3.1.2
      - orjson==3.10.6
      - outcome==1.2.0
      - overrides==7.7.0
      - packaging==23.2
      - pandas==2.0.1
      - pandocfilters==1.5.0
      - parse==1.19.0
      - parso==0.8.4
      - pathtools==0.1.2
      - pathy==0.10.2
      - pexpect==4.9.0
      - pillow==9.5.0
      - pipreqs==0.4.13
      - pkginfo==1.9.6
      - platformdirs==2.6.2
      - poetry==1.4.2
      - poetry-core==1.5.2
      - poetry-plugin-export==1.3.0
      - preshed==3.0.8
      - prometheus-client==0.16.0
      - prompt-toolkit==3.0.47
      - protobuf==4.23.2
      - psutil==6.0.0
      - ptyprocess==0.7.0
      - pure-eval==0.2.2
      - pyarrow==11.0.0
      - pyasn1==0.5.0
      - pyasn1-modules==0.3.0
      - pycparser==2.21
      - pydantic==1.10.7
      - pydantic-core==2.20.1
      - pyee==8.2.2
      - pygments==2.18.0
      - pynvml==11.5.3
      - pyparsing==3.0.9
      - pypdf==4.3.1
      - pyppeteer==1.0.2
      - pyproject-hooks==1.0.0
      - pyquery==2.0.0
      - pyrsistent==0.19.3
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.0.1
      - python-json-logger==2.0.7
      - pytz==2023.3
      - pyzmq==26.0.3
      - qtconsole==5.4.2
      - qtpy==2.3.1
      - ragatouille==0.0.8.post2
      - rapidfuzz==2.15.1
      - recognizers-text==1.0.2a2
      - recognizers-text-choice==1.0.2a2
      - recognizers-text-date-time==1.0.2a2
      - recognizers-text-number==1.0.2a2
      - recognizers-text-number-with-unit==1.0.2a2
      - recognizers-text-sequence==1.0.2a2
      - recognizers-text-suite==1.0.2a2
      - regex==2023.3.23
      - requests==2.31.0
      - requests-html==0.10.0
      - requests-toolbelt==0.10.1
      - responses==0.18.0
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rich==13.3.4
      - rsa==4.9
      - safetensors==0.4.3
      - scikit-learn==1.2.2
      - scipy==1.10.1
      - scrapeghost==0.4.4
      - secretstorage==3.3.3
      - selenium==4.9.0
      - send2trash==1.8.0
      - sentence-transformers==2.2.2
      - sentencepiece==0.1.99
      - sentry-sdk==1.24.0
      - setproctitle==1.3.2
      - shellingham==1.5.0.post1
      - six==1.16.0
      - smart-open==6.3.0
      - smmap==5.0.0
      - sniffio==1.3.0
      - sortedcontainers==2.4.0
      - soupsieve==2.4.1
      - spacy==3.6.0
      - spacy-legacy==3.0.12
      - spacy-loggers==1.0.4
      - sqlalchemy==2.0.31
      - srsly==2.4.8
      - stack-data==0.6.3
      - striprtf==0.0.26
      - structlog==22.3.0
      - super-search-gpt==0.1.4
      - tabulate==0.9.0
      - tenacity==8.2.2
      - terminado==0.17.1
      - thinc==8.1.10
      - threadpoolctl==3.1.0
      - tiktoken==0.3.3
      - tinycss2==1.2.1
      - tokenizers==0.15.2
      - tomli==2.0.1
      - tomlkit==0.11.7
      - torch==2.3.1
      - torch-scatter==2.1.2
      - torchaudio==2.3.1
      - torchvision==0.18.1
      - tornado==6.4.1
      - tqdm==4.66.4
      - traitlets==5.14.3
      - transformers==4.36.2
      - trio==0.22.0
      - trio-websocket==0.10.2
      - triton==2.3.1
      - trove-classifiers==2023.3.9
      - typer==0.7.0
      - typing-extensions==4.12.2
      - typing-inspect==0.8.0
      - tzdata==2023.3
      - ujson==5.10.0
      - uri-template==1.2.0
      - uritemplate==4.1.1
      - urllib3==1.26.15
      - virtualenv==20.21.0
      - voyager==2.0.8
      - w3lib==2.1.1
      - wandb==0.15.3
      - wasabi==1.1.2
      - wcwidth==0.2.13
      - weasel==0.4.1
      - webcolors==1.13
      - webencodings==0.5.1
      - websocket-client==1.5.1
      - websockets==10.4
      - werkzeug==3.0.3
      - widgetsnbextension==4.0.7
      - wikiextractor==3.0.6
      - wikipedia==1.4.0
      - wrapt==1.16.0
      - wsproto==1.2.0
      - xattr==0.10.1
      - xxhash==3.2.0
      - yarg==0.1.9
      - yarl==1.8.2
      - zipp==3.19.2
prefix: /home/<USER>/miniconda3/envs/tablerag
