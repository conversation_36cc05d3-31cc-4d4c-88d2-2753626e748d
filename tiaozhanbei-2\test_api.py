#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智谱AI API连接测试脚本
"""

from llama_index.llms.openai_like import OpenAILike

def test_zhipu_api():
    """测试智谱AI API连接"""
    print("🔍 测试智谱AI API连接...")
    
    try:
        # 使用与app.py相同的配置
        llm = OpenAILike(
            model="glm-4",
            api_base="https://open.bigmodel.cn/api/paas/v4",
            api_key="y6d08ce2f1f3b41ee807d0759702423cf.MavyRw2x7Q4NVNr8",
            context_window=128000,
            is_chat_model=True,
            is_function_calling_model=False,
            max_tokens=100,  # 测试时使用较小的token数
            temperature=0.3,
            top_p=0.7,
            timeout=30,
            max_retries=2
        )
        
        print("📡 发送测试请求...")
        
        # 发送一个简单的测试请求
        response = llm.complete("你好，请简单介绍一下自己。")
        
        print("✅ API连接成功！")
        print(f"📝 响应内容: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {str(e)}")
        
        # 提供一些常见问题的解决建议
        error_str = str(e).lower()
        if "unauthorized" in error_str or "401" in error_str:
            print("💡 可能的原因: API密钥无效")
            print("   请检查智谱AI API密钥是否正确")
        elif "timeout" in error_str or "connection" in error_str:
            print("💡 可能的原因: 网络连接问题")
            print("   请检查网络连接或稍后重试")
        elif "404" in error_str:
            print("💡 可能的原因: API端点地址错误")
            print("   请检查API base URL是否正确")
        else:
            print("💡 建议: 检查API配置和网络连接")
        
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 智谱AI API连接测试")
    print("=" * 50)
    
    success = test_zhipu_api()
    
    if success:
        print("\n🎉 测试通过！可以正常使用智谱AI API")
    else:
        print("\n🔧 请根据上述提示解决问题后重试")
    
    input("\n按回车键退出...")
