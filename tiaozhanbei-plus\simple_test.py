# simple_test.py
"""
简单测试修复后的输出处理器
"""

def test_output_processor():
    """测试输出处理器"""
    print("=== 测试输出处理器 ===")
    
    # 模拟有问题的输出
    problematic_output = """
根据您的查询，您需要获取十二里河渡槽进口节制闸在2020-01-01期间的调度数据。

```python
# 首先检查时间列的数据类型和格式
print(df['时间'].dtype)

# 将时间列转换为datetime类型
df['时间'] = pd.to_datetime(df['时间'])

# 显示结果
print(df.head())
```

如果数据中没有包含2020-01-01的记录，我会提供数据的时间范围供参考。
"""
    
    try:
        # 导入输出处理器
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app import custom_output_processor
        
        # 测试处理器
        cleaned_code = custom_output_processor(problematic_output, "测试查询")
        
        print("✓ 输出处理器导入成功")
        print("\n原始输出（前200字符）:")
        print(problematic_output[:200] + "...")
        
        print("\n处理后的代码:")
        print(cleaned_code)
        
        # 检查处理结果
        if "```python" not in cleaned_code and "```" not in cleaned_code:
            print("\n✓ Markdown标记清理成功")
        else:
            print("\n✗ Markdown标记未完全清理")
        
        if "print(df" in cleaned_code or "df.head()" in cleaned_code:
            print("✓ 包含有效的Pandas代码")
        else:
            print("✗ 缺少有效的Pandas代码")
        
        print("\n✓ 输出处理器测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 输出处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        # 测试基本导入
        from app import Config, load_excel_dataframes
        
        print("✓ 基本模块导入成功")
        print(f"数据目录: {Config.DATA_DIR}")
        print(f"使用TableRAG: {Config.USE_TABLERAG}")
        print(f"Excel处理模式: {Config.EXCEL_PROCESSING_MODE}")
        
        # 检查数据文件
        from pathlib import Path
        data_dir = Path(Config.DATA_DIR)
        excel_files = list(data_dir.rglob("*.xlsx"))
        
        print(f"发现 {len(excel_files)} 个Excel文件")
        
        if excel_files:
            # 尝试加载DataFrame
            dataframes = load_excel_dataframes(Config.DATA_DIR)
            print(f"✓ 成功加载 {len(dataframes)} 个DataFrame")
            
            for name, df in dataframes.items():
                print(f"  - {name}: {df.shape}")
                break  # 只显示第一个
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("简单修复测试")
    print("=" * 40)
    
    # 测试输出处理器
    processor_success = test_output_processor()
    
    # 测试基本功能
    basic_success = test_basic_functionality()
    
    print("\n" + "=" * 40)
    print("测试结果:")
    print(f"输出处理器: {'✓ 成功' if processor_success else '✗ 失败'}")
    print(f"基本功能: {'✓ 成功' if basic_success else '✗ 失败'}")
    
    if processor_success and basic_success:
        print("\n🎉 修复测试成功！")
        print("\n💡 现在可以启动应用测试:")
        print("streamlit run app.py")
        print("\n建议尝试的查询:")
        print("1. '这个表格有多少行数据？'")
        print("2. '显示前5行数据'")
        print("3. '表格的列名是什么？'")
    else:
        print("\n⚠️ 还有问题需要解决")


if __name__ == "__main__":
    main()
