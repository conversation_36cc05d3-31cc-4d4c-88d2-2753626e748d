# tablerag_config.py
"""
TableRAG集成配置文件
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional

class TableRAGIntegrationConfig:
    """TableRAG集成配置类"""
    
    def __init__(self):
        # 基础配置
        self.enabled = True
        self.fallback_to_simple = True  # 如果TableRAG失败，是否回退到简单处理
        
        # TableRAG处理配置
        self.embedding_type = "text-embedding-3-large"
        self.table_filter_name = "llm_based_filter"  # "llm_based_filter", "semantics_based_filter", "None"
        self.table_clarifier_name = "term_explanations_and_table_summary"  # "None", "term_explanations", "table_summary", "term_explanations_and_table_summary"
        self.table_format = "markdown"  # "markdown", "html", "string"
        self.top_k = 5
        self.use_table_filter = True
        self.use_self_consistency = False
        
        # 性能配置
        self.max_table_size = 1000  # 最大处理的表格行数
        self.max_content_length = 5000  # 最大内容长度
        self.parallel_processing = True
        self.max_workers = 4
        
        # 缓存配置
        self.enable_cache = True
        self.cache_dir = "./tablerag_cache"
        
        # API配置（如果使用OpenAI等服务）
        self.api_key = os.getenv("OPENAI_API_KEY", "")
        self.api_base = os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
        self.model_name = "gpt-4o-mini"
        
        # 日志配置
        self.log_level = "INFO"
        self.log_file = "./logs/tablerag.log"
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "enabled": self.enabled,
            "fallback_to_simple": self.fallback_to_simple,
            "embedding_type": self.embedding_type,
            "table_filter_name": self.table_filter_name,
            "table_clarifier_name": self.table_clarifier_name,
            "table_format": self.table_format,
            "top_k": self.top_k,
            "use_table_filter": self.use_table_filter,
            "use_self_consistency": self.use_self_consistency,
            "max_table_size": self.max_table_size,
            "max_content_length": self.max_content_length,
            "parallel_processing": self.parallel_processing,
            "max_workers": self.max_workers,
            "enable_cache": self.enable_cache,
            "cache_dir": self.cache_dir,
            "api_key": self.api_key,
            "api_base": self.api_base,
            "model_name": self.model_name,
            "log_level": self.log_level,
            "log_file": self.log_file
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TableRAGIntegrationConfig':
        """从字典创建配置实例"""
        config = cls()
        for key, value in config_dict.items():
            if hasattr(config, key):
                setattr(config, key, value)
        return config
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        valid_filters = ["llm_based_filter", "semantics_based_filter", "None"]
        valid_clarifiers = ["None", "term_explanations", "table_summary", "term_explanations_and_table_summary"]
        valid_formats = ["markdown", "html", "string"]
        
        if self.table_filter_name not in valid_filters:
            print(f"警告: 无效的table_filter_name: {self.table_filter_name}")
            return False
            
        if self.table_clarifier_name not in valid_clarifiers:
            print(f"警告: 无效的table_clarifier_name: {self.table_clarifier_name}")
            return False
            
        if self.table_format not in valid_formats:
            print(f"警告: 无效的table_format: {self.table_format}")
            return False
            
        if self.top_k <= 0:
            print(f"警告: top_k必须大于0: {self.top_k}")
            return False
            
        return True


# 预定义配置模板
TABLERAG_CONFIGS = {
    "basic": {
        "enabled": True,
        "table_filter_name": "None",
        "table_clarifier_name": "None",
        "table_format": "string",
        "use_table_filter": False
    },
    
    "enhanced": {
        "enabled": True,
        "table_filter_name": "llm_based_filter",
        "table_clarifier_name": "term_explanations_and_table_summary",
        "table_format": "markdown",
        "use_table_filter": True
    },
    
    "performance": {
        "enabled": True,
        "table_filter_name": "semantics_based_filter",
        "table_clarifier_name": "table_summary",
        "table_format": "html",
        "use_table_filter": True,
        "parallel_processing": True,
        "max_workers": 8
    },
    
    "minimal": {
        "enabled": True,
        "table_filter_name": "None",
        "table_clarifier_name": "None",
        "table_format": "string",
        "use_table_filter": False,
        "use_self_consistency": False,
        "max_table_size": 500,  # 限制表格大小
        "max_content_length": 2000,  # 限制内容长度
        "parallel_processing": False,  # 关闭并行处理节省内存
        "enable_cache": True  # 启用缓存减少重复计算
    },

    "low_memory": {
        "enabled": True,
        "table_filter_name": "None",  # 不使用过滤器节省显存
        "table_clarifier_name": "None",  # 不使用澄清器节省显存
        "table_format": "string",  # 使用最简单的格式
        "use_table_filter": False,
        "use_self_consistency": False,
        "max_table_size": 300,  # 更严格的表格大小限制
        "max_content_length": 1500,  # 更严格的内容长度限制
        "parallel_processing": False,
        "max_workers": 1,  # 单线程处理
        "enable_cache": True,
        "api_key": "",  # 不使用API调用，避免额外开销
        "log_level": "ERROR"  # 减少日志输出
    }
}


def get_tablerag_config(config_name: str = "enhanced") -> TableRAGIntegrationConfig:
    """
    获取预定义的TableRAG配置
    
    Args:
        config_name: 配置名称 ("basic", "enhanced", "performance", "minimal")
        
    Returns:
        TableRAGIntegrationConfig实例
    """
    if config_name not in TABLERAG_CONFIGS:
        print(f"警告: 未知的配置名称 '{config_name}'，使用默认配置")
        config_name = "enhanced"
    
    base_config = TableRAGIntegrationConfig()
    preset_config = TABLERAG_CONFIGS[config_name]
    
    # 应用预设配置
    for key, value in preset_config.items():
        if hasattr(base_config, key):
            setattr(base_config, key, value)
    
    return base_config


def create_tablerag_config_from_file(config_file: str) -> Optional[TableRAGIntegrationConfig]:
    """
    从JSON文件加载TableRAG配置
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        TableRAGIntegrationConfig实例或None
    """
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        config = TableRAGIntegrationConfig.from_dict(config_dict)
        
        if config.validate():
            return config
        else:
            print(f"配置文件 {config_file} 验证失败")
            return None
            
    except Exception as e:
        print(f"加载配置文件 {config_file} 时出错: {e}")
        return None


def save_tablerag_config(config: TableRAGIntegrationConfig, config_file: str) -> bool:
    """
    保存TableRAG配置到JSON文件
    
    Args:
        config: TableRAGIntegrationConfig实例
        config_file: 配置文件路径
        
    Returns:
        是否保存成功
    """
    try:
        import json
        
        # 确保目录存在
        config_path = Path(config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
        
        print(f"配置已保存到 {config_file}")
        return True
        
    except Exception as e:
        print(f"保存配置文件 {config_file} 时出错: {e}")
        return False


# 示例用法
if __name__ == "__main__":
    # 创建并保存示例配置
    config = get_tablerag_config("enhanced")
    save_tablerag_config(config, "tablerag_config.json")
    
    # 加载配置
    loaded_config = create_tablerag_config_from_file("tablerag_config.json")
    if loaded_config:
        print("配置加载成功")
        print(f"启用状态: {loaded_config.enabled}")
        print(f"表格格式: {loaded_config.table_format}")
        print(f"过滤器: {loaded_config.table_filter_name}")
        print(f"澄清器: {loaded_config.table_clarifier_name}")
